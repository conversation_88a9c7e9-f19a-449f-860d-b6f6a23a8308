// 云学堂视频加速器 - 精准版
// 针对增量学习时间提交的精准解决方案

(function() {
    'use strict';
    
    console.log('%c🚀 云学堂视频加速器 - 精准版启动', 'color: #ff6b35; font-weight: bold; font-size: 16px;');
    
    // 全局变量
    let currentVideo = null;
    let controlPanel = null;
    let bypassTimer = null;
    let currentSpeed = 1;
    
    // 学习时间精准追踪
    let realStartTime = 0;
    let lastSubmitTime = 0;
    let totalSubmittedSeconds = 0;
    let skipCount = 0;
    
    // 拦截网络请求
    let originalXHR = window.XMLHttpRequest.prototype.open;
    
    // 查找视频元素
    function findVideo() {
        const videos = document.querySelectorAll('video');
        console.log(`🔍 找到 ${videos.length} 个视频元素`);
        
        if (videos.length > 0) {
            currentVideo = videos[0];
            console.log('✅ 使用视频元素:', currentVideo);
            return currentVideo;
        }
        return null;
    }
    
    // 精准拦截学习时间提交
    function setupPrecisionInterception() {
        console.log('🎯 设置精准学习时间拦截...');
        
        // 拦截 XMLHttpRequest
        window.XMLHttpRequest.prototype.open = function(method, url, ...rest) {
            this._url = url;
            this._method = method;
            
            // 专门拦截学习秒数提交API
            if (url.includes('/kng/study/submit/second')) {
                console.log('🎯 拦截到学习秒数提交API:', url);
                
                const originalSend = this.send;
                this.send = function(data) {
                    if (data) {
                        try {
                            let sendData = JSON.parse(data);
                            console.log('📊 原始学习数据:', sendData);
                            
                            // 计算应该提交的学习时间
                            const now = Date.now();
                            const realElapsed = (now - realStartTime) / 1000;
                            const shouldSubmit = Math.floor(realElapsed - totalSubmittedSeconds);
                            
                            // 修改关键字段
                            if (sendData.acqSecond !== undefined) {
                                const originalAcq = sendData.acqSecond;
                                sendData.acqSecond = Math.max(shouldSubmit, originalAcq);
                                console.log(`📝 修改acqSecond: ${originalAcq} -> ${sendData.acqSecond}`);
                            }
                            
                            if (sendData.actualSecond !== undefined) {
                                const originalActual = sendData.actualSecond;
                                sendData.actualSecond = Math.max(shouldSubmit, originalActual);
                                console.log(`📝 修改actualSecond: ${originalActual} -> ${sendData.actualSecond}`);
                            }
                            
                            // 确保速度为正常速度
                            if (sendData.speed !== undefined) {
                                sendData.speed = 1;
                                console.log(`📝 修改speed: -> 1`);
                            }
                            
                            // 更新已提交总时间
                            totalSubmittedSeconds += sendData.acqSecond || 0;
                            
                            data = JSON.stringify(sendData);
                            console.log('✅ 修改后学习数据:', sendData);
                            console.log(`📈 累计已提交: ${totalSubmittedSeconds}秒`);
                            
                        } catch (e) {
                            console.log('⚠️ 无法解析学习数据:', e);
                        }
                    }
                    
                    return originalSend.call(this, data);
                };
            }
            
            return originalXHR.apply(this, [method, url, ...rest]);
        };
        
        console.log('✅ 精准学习时间拦截设置完成');
    }
    
    // 创建控制面板
    function createControlPanel() {
        const existing = document.getElementById('yxt-precision-panel');
        if (existing) existing.remove();
        
        const panel = document.createElement('div');
        panel.id = 'yxt-precision-panel';
        panel.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            width: 320px !important;
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
            color: white !important;
            padding: 16px !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5) !important;
            z-index: 2147483647 !important;
            font-family: Arial, sans-serif !important;
            font-size: 14px !important;
            border: 2px solid rgba(255,255,255,0.3) !important;
        `;
        
        panel.innerHTML = `
            <div style="font-weight: bold; font-size: 16px; margin-bottom: 12px; text-align: center;">
                🎯 精准视频加速器
            </div>
            
            <div style="margin-bottom: 12px;">
                <div style="margin-bottom: 8px; font-weight: 500;">播放速度:</div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 6px; margin-bottom: 8px;">
                    <button class="speed-btn" data-speed="5" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">5x</button>
                    <button class="speed-btn" data-speed="8" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">8x</button>
                    <button class="speed-btn" data-speed="10" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">10x</button>
                    <button class="speed-btn" data-speed="15" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">15x</button>
                    <button class="speed-btn" data-speed="20" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">20x</button>
                    <button class="speed-btn" data-speed="30" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">30x</button>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px;">
                    <button id="start-precision" style="padding: 8px; border: none; border-radius: 4px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">开始精准加速</button>
                    <button id="stop-precision" style="padding: 8px; border: none; border-radius: 4px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">停止加速</button>
                </div>
            </div>
            
            <div style="font-size: 12px; line-height: 1.4; background: rgba(0,0,0,0.2); padding: 8px; border-radius: 6px;">
                <div>目标速度: <span id="target-speed" style="font-weight: bold; color: #ffeb3b;">1x</span></div>
                <div>加速状态: <span id="bypass-status" style="font-weight: bold; color: #4caf50;">待机</span></div>
                <div>播放进度: <span id="progress-display" style="font-weight: bold; color: #2196f3;">0%</span></div>
                <div>实际学习: <span id="real-time" style="font-weight: bold; color: #ff9800;">0秒</span></div>
                <div>已提交: <span id="submitted-time" style="font-weight: bold; color: #9c27b0;">0秒</span></div>
                <div>拦截状态: <span id="intercept-status" style="font-weight: bold; color: #00bcd4;">已启用</span></div>
            </div>
            
            <div style="margin-top: 10px; font-size: 11px; opacity: 0.8; text-align: center;">
                精准版 v7.0 | 针对增量提交优化
            </div>
        `;
        
        document.body.appendChild(panel);
        controlPanel = panel;
        
        console.log('✅ 精准版控制面板创建成功');
        return panel;
    }
    
    // 精准加速模式
    function precisionMode(targetSpeed) {
        if (bypassTimer) clearInterval(bypassTimer);
        
        // 设置基础播放速度
        currentVideo.playbackRate = Math.min(targetSpeed, 2);
        
        // 计算跳跃参数
        const skipInterval = 1000; // 每秒检查一次
        const skipAmount = targetSpeed > 2 ? (targetSpeed - 2) : 0;
        
        console.log(`🎯 精准模式参数: 基础速度${Math.min(targetSpeed, 2)}x, 跳跃量${skipAmount}s/秒`);
        
        bypassTimer = setInterval(() => {
            if (!currentVideo || currentVideo.paused) return;
            
            const currentTime = currentVideo.currentTime;
            const duration = currentVideo.duration;
            
            // 执行时间跳跃
            if (skipAmount > 0 && currentTime < duration - 5) {
                const newTime = Math.min(currentTime + skipAmount, duration - 1);
                currentVideo.currentTime = newTime;
                skipCount++;
                
                console.log(`⚡ 精准跳跃: ${currentTime.toFixed(1)}s -> ${newTime.toFixed(1)}s`);
                
                // 触发进度事件
                triggerProgressEvents();
            }
            
            // 更新显示
            updateDisplays();
            
        }, skipInterval);
        
        console.log(`🎯 精准模式启动: ${targetSpeed}x`);
    }
    
    // 触发进度事件
    function triggerProgressEvents() {
        if (!currentVideo) return;
        
        const events = ['timeupdate', 'progress', 'seeking', 'seeked'];
        events.forEach(eventType => {
            const event = new Event(eventType, { bubbles: true });
            currentVideo.dispatchEvent(event);
        });
    }
    
    // 启动精准加速
    function startPrecision(speed) {
        stopPrecision();
        
        currentSpeed = speed;
        realStartTime = Date.now();
        totalSubmittedSeconds = 0;
        skipCount = 0;
        
        updateTargetSpeed(speed);
        updateBypassStatus('精准运行中');
        updateRealTime('0秒');
        updateSubmittedTime('0秒');
        
        // 启动精准模式
        precisionMode(speed);
        
        console.log(`🚀 开始精准加速: ${speed}x`);
    }
    
    // 停止精准加速
    function stopPrecision() {
        if (bypassTimer) {
            clearInterval(bypassTimer);
            bypassTimer = null;
        }
        
        if (currentVideo) {
            currentVideo.playbackRate = 1;
        }
        
        updateBypassStatus('已停止');
        console.log('⏹️ 精准加速已停止');
    }
    
    // 设置面板事件
    function setupPanelEvents() {
        if (!controlPanel) return;
        
        // 速度按钮
        const speedButtons = controlPanel.querySelectorAll('.speed-btn');
        speedButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const speed = parseFloat(btn.dataset.speed);
                startPrecision(speed);
                
                speedButtons.forEach(b => b.style.background = 'rgba(255,255,255,0.2)');
                btn.style.background = 'rgba(255,255,255,0.5)';
            });
        });
        
        // 开始/停止按钮
        controlPanel.querySelector('#start-precision').addEventListener('click', () => {
            startPrecision(currentSpeed > 1 ? currentSpeed : 10);
        });
        
        controlPanel.querySelector('#stop-precision').addEventListener('click', stopPrecision);
        
        console.log('✅ 精准版面板事件设置完成');
    }
    
    // 更新显示
    function updateDisplays() {
        if (!realStartTime) return;
        
        const now = Date.now();
        const realElapsed = (now - realStartTime) / 1000;
        
        updateRealTime(Math.floor(realElapsed) + '秒');
        updateSubmittedTime(totalSubmittedSeconds + '秒');
        
        // 更新进度
        if (currentVideo) {
            const progress = currentVideo.duration ? 
                (currentVideo.currentTime / currentVideo.duration * 100).toFixed(1) + '%' : '0%';
            updateProgressDisplay(progress);
        }
    }
    
    // 更新显示函数
    function updateTargetSpeed(speed) {
        const el = controlPanel?.querySelector('#target-speed');
        if (el) el.textContent = speed + 'x';
    }
    
    function updateBypassStatus(status) {
        const el = controlPanel?.querySelector('#bypass-status');
        if (el) el.textContent = status;
    }
    
    function updateProgressDisplay(progress) {
        const el = controlPanel?.querySelector('#progress-display');
        if (el) el.textContent = progress;
    }
    
    function updateRealTime(time) {
        const el = controlPanel?.querySelector('#real-time');
        if (el) el.textContent = time;
    }
    
    function updateSubmittedTime(time) {
        const el = controlPanel?.querySelector('#submitted-time');
        if (el) el.textContent = time;
    }
    
    function updateInterceptStatus(status) {
        const el = controlPanel?.querySelector('#intercept-status');
        if (el) el.textContent = status;
    }
    
    // 启动自动更新
    function startAutoUpdate() {
        setInterval(() => {
            if (realStartTime > 0) {
                updateDisplays();
            }
        }, 1000);
    }
    
    // 初始化
    function init() {
        console.log('🔧 开始初始化精准版...');
        
        // 首先设置精准拦截
        setupPrecisionInterception();
        
        createControlPanel();
        updateInterceptStatus('已启用');
        
        if (findVideo()) {
            updateBypassStatus('已找到视频');
        } else {
            updateBypassStatus('未找到视频');
        }
        
        setupPanelEvents();
        startAutoUpdate();
        
        console.log('✅ 精准版初始化完成');
        console.log('💡 使用说明:');
        console.log('   1. 精准拦截学习秒数提交API');
        console.log('   2. 点击速度按钮开始精准加速');
        console.log('   3. 观察"已提交"时间与"实际学习"时间');
        console.log('   4. 确保学习时间正确累计');
    }
    
    // 提供全局访问
    window.yxtPrecisionController = {
        start: startPrecision,
        stop: stopPrecision,
        getVideo: () => currentVideo,
        getSubmittedTime: () => totalSubmittedSeconds,
        getRealTime: () => realStartTime > 0 ? (Date.now() - realStartTime) / 1000 : 0
    };
    
    // 启动
    init();
    
    console.log('🎉 精准版加载完成！');
    console.log('🎯 这个版本专门针对云学堂的增量学习时间提交机制');
    
})();
