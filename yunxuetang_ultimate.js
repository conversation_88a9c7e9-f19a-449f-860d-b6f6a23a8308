// 云学堂视频加速器 - 终极版
// 深度拦截和模拟学习时间记录

(function() {
    'use strict';
    
    console.log('%c🚀 云学堂视频加速器 - 终极版启动', 'color: #ff6b35; font-weight: bold; font-size: 16px;');
    
    // 全局变量
    let currentVideo = null;
    let controlPanel = null;
    let bypassTimer = null;
    let heartbeatTimer = null;
    let currentSpeed = 1;
    
    // 学习时间追踪
    let realStartTime = 0;
    let accumulatedWatchTime = 0;
    let lastHeartbeatTime = 0;
    let skipCount = 0;
    
    // 拦截网络请求
    let originalFetch = window.fetch;
    let originalXHR = window.XMLHttpRequest.prototype.open;
    
    // 查找视频元素
    function findVideo() {
        const videos = document.querySelectorAll('video');
        console.log(`🔍 找到 ${videos.length} 个视频元素`);
        
        if (videos.length > 0) {
            currentVideo = videos[0];
            console.log('✅ 使用视频元素:', currentVideo);
            return currentVideo;
        }
        return null;
    }
    
    // 拦截和修改网络请求
    function setupNetworkInterception() {
        console.log('🕸️ 设置网络请求拦截...');
        
        // 拦截 fetch 请求
        window.fetch = function(...args) {
            const url = args[0];
            const options = args[1] || {};
            
            if (typeof url === 'string') {
                // 检查是否是学习进度相关的API
                if (url.includes('progress') || url.includes('heartbeat') || url.includes('study') || url.includes('learn')) {
                    console.log('🎯 拦截到学习相关请求:', url);
                    
                    // 修改请求数据
                    if (options.body) {
                        try {
                            let bodyData = JSON.parse(options.body);
                            
                            // 修改学习时间相关字段
                            if (bodyData.watchTime !== undefined) {
                                bodyData.watchTime = accumulatedWatchTime;
                                console.log(`📝 修改watchTime: ${bodyData.watchTime}`);
                            }
                            if (bodyData.studyTime !== undefined) {
                                bodyData.studyTime = accumulatedWatchTime;
                                console.log(`📝 修改studyTime: ${bodyData.studyTime}`);
                            }
                            if (bodyData.duration !== undefined) {
                                bodyData.duration = accumulatedWatchTime;
                                console.log(`📝 修改duration: ${bodyData.duration}`);
                            }
                            
                            options.body = JSON.stringify(bodyData);
                        } catch (e) {
                            console.log('⚠️ 无法解析请求体:', e);
                        }
                    }
                }
            }
            
            return originalFetch.apply(this, [url, options]);
        };
        
        // 拦截 XMLHttpRequest
        window.XMLHttpRequest.prototype.open = function(method, url, ...rest) {
            this._url = url;
            this._method = method;
            
            if (url.includes('progress') || url.includes('heartbeat') || url.includes('study') || url.includes('learn')) {
                console.log('🎯 拦截到XHR学习请求:', url);
                
                // 拦截发送数据
                const originalSend = this.send;
                this.send = function(data) {
                    if (data) {
                        try {
                            let sendData = JSON.parse(data);
                            
                            // 修改学习时间
                            if (sendData.watchTime !== undefined) {
                                sendData.watchTime = accumulatedWatchTime;
                            }
                            if (sendData.studyTime !== undefined) {
                                sendData.studyTime = accumulatedWatchTime;
                            }
                            if (sendData.duration !== undefined) {
                                sendData.duration = accumulatedWatchTime;
                            }
                            
                            data = JSON.stringify(sendData);
                            console.log('📝 修改XHR发送数据:', sendData);
                        } catch (e) {
                            console.log('⚠️ 无法解析XHR数据:', e);
                        }
                    }
                    
                    return originalSend.call(this, data);
                };
            }
            
            return originalXHR.apply(this, [method, url, ...rest]);
        };
        
        console.log('✅ 网络请求拦截设置完成');
    }
    
    // 创建控制面板
    function createControlPanel() {
        const existing = document.getElementById('yxt-ultimate-panel');
        if (existing) existing.remove();
        
        const panel = document.createElement('div');
        panel.id = 'yxt-ultimate-panel';
        panel.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            width: 320px !important;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
            color: white !important;
            padding: 16px !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5) !important;
            z-index: 2147483647 !important;
            font-family: Arial, sans-serif !important;
            font-size: 14px !important;
            border: 2px solid rgba(255,255,255,0.3) !important;
        `;
        
        panel.innerHTML = `
            <div style="font-weight: bold; font-size: 16px; margin-bottom: 12px; text-align: center;">
                🔥 终极视频加速器
            </div>
            
            <div style="margin-bottom: 12px;">
                <div style="margin-bottom: 8px; font-weight: 500;">播放速度:</div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 6px; margin-bottom: 8px;">
                    <button class="speed-btn" data-speed="5" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">5x</button>
                    <button class="speed-btn" data-speed="8" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">8x</button>
                    <button class="speed-btn" data-speed="10" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">10x</button>
                    <button class="speed-btn" data-speed="15" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">15x</button>
                    <button class="speed-btn" data-speed="20" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">20x</button>
                    <button class="speed-btn" data-speed="30" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">30x</button>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px;">
                    <button id="start-ultimate" style="padding: 8px; border: none; border-radius: 4px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">开始终极加速</button>
                    <button id="stop-ultimate" style="padding: 8px; border: none; border-radius: 4px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">停止加速</button>
                </div>
            </div>
            
            <div style="font-size: 12px; line-height: 1.4; background: rgba(0,0,0,0.2); padding: 8px; border-radius: 6px;">
                <div>目标速度: <span id="target-speed" style="font-weight: bold; color: #ffeb3b;">1x</span></div>
                <div>加速状态: <span id="bypass-status" style="font-weight: bold; color: #4caf50;">待机</span></div>
                <div>播放进度: <span id="progress-display" style="font-weight: bold; color: #2196f3;">0%</span></div>
                <div>累计学习: <span id="study-time" style="font-weight: bold; color: #ff9800;">0分钟</span></div>
                <div>网络拦截: <span id="network-status" style="font-weight: bold; color: #9c27b0;">已启用</span></div>
                <div>心跳发送: <span id="heartbeat-status" style="font-weight: bold; color: #00bcd4;">正常</span></div>
            </div>
            
            <div style="margin-top: 10px; font-size: 11px; opacity: 0.8; text-align: center;">
                终极版 v6.0 | 深度拦截学习时间
            </div>
        `;
        
        document.body.appendChild(panel);
        controlPanel = panel;
        
        console.log('✅ 终极版控制面板创建成功');
        return panel;
    }
    
    // 终极加速模式
    function ultimateMode(targetSpeed) {
        if (bypassTimer) clearInterval(bypassTimer);
        
        // 设置基础播放速度
        currentVideo.playbackRate = Math.min(targetSpeed, 2);
        
        // 计算跳跃参数
        const skipInterval = 500; // 每500ms检查一次
        const skipAmount = targetSpeed > 2 ? (targetSpeed - 2) * 0.5 : 0;
        
        console.log(`🔥 终极模式参数: 基础速度${Math.min(targetSpeed, 2)}x, 跳跃量${skipAmount}s`);
        
        bypassTimer = setInterval(() => {
            if (!currentVideo || currentVideo.paused) return;
            
            const currentTime = currentVideo.currentTime;
            const duration = currentVideo.duration;
            
            // 执行时间跳跃
            if (skipAmount > 0 && currentTime < duration - 5) {
                const newTime = Math.min(currentTime + skipAmount, duration - 1);
                currentVideo.currentTime = newTime;
                skipCount++;
                
                console.log(`⚡ 终极跳跃: ${currentTime.toFixed(1)}s -> ${newTime.toFixed(1)}s`);
                
                // 强制触发所有可能的事件
                triggerAllEvents();
            }
            
            // 更新累计学习时间（按正常速度计算）
            updateAccumulatedTime();
            
        }, skipInterval);
        
        console.log(`🎯 终极模式启动: ${targetSpeed}x`);
    }
    
    // 触发所有可能的事件
    function triggerAllEvents() {
        if (!currentVideo) return;
        
        // HTML5 标准事件
        const events = ['timeupdate', 'progress', 'seeking', 'seeked', 'loadeddata', 'canplay'];
        events.forEach(eventType => {
            const event = new Event(eventType, { bubbles: true, cancelable: true });
            currentVideo.dispatchEvent(event);
        });
        
        // 自定义事件
        const customEvents = ['video-progress', 'study-progress', 'learn-progress'];
        customEvents.forEach(eventType => {
            window.dispatchEvent(new CustomEvent(eventType, {
                detail: {
                    currentTime: currentVideo.currentTime,
                    duration: currentVideo.duration,
                    studyTime: accumulatedWatchTime,
                    watchTime: accumulatedWatchTime
                }
            }));
        });
        
        // 尝试触发Vue组件事件
        try {
            if (window.Vue) {
                const vueElements = document.querySelectorAll('[data-v-*], [class*="vue"]');
                vueElements.forEach(el => {
                    if (el.__vue__) {
                        ['timeupdate', 'progress', 'study-update'].forEach(event => {
                            if (el.__vue__.$emit) {
                                el.__vue__.$emit(event, {
                                    currentTime: currentVideo.currentTime,
                                    studyTime: accumulatedWatchTime
                                });
                            }
                        });
                    }
                });
            }
        } catch (e) {
            // 忽略错误
        }
    }
    
    // 更新累计学习时间
    function updateAccumulatedTime() {
        const now = Date.now();
        if (realStartTime > 0) {
            // 按正常速度计算学习时间
            accumulatedWatchTime = (now - realStartTime) / 1000;
            
            const minutes = Math.floor(accumulatedWatchTime / 60);
            const seconds = Math.floor(accumulatedWatchTime % 60);
            updateStudyTimeDisplay(`${minutes}分${seconds}秒`);
        }
    }
    
    // 启动心跳包发送
    function startHeartbeat() {
        if (heartbeatTimer) clearInterval(heartbeatTimer);
        
        heartbeatTimer = setInterval(() => {
            if (!currentVideo || currentVideo.paused) return;
            
            // 发送模拟的学习心跳包
            try {
                // 尝试调用可能的心跳API
                const heartbeatData = {
                    currentTime: currentVideo.currentTime,
                    duration: currentVideo.duration,
                    studyTime: accumulatedWatchTime,
                    watchTime: accumulatedWatchTime,
                    progress: (currentVideo.currentTime / currentVideo.duration * 100).toFixed(2),
                    timestamp: Date.now()
                };
                
                console.log('💓 发送学习心跳包:', heartbeatData);
                
                // 触发可能的心跳事件
                window.dispatchEvent(new CustomEvent('study-heartbeat', {
                    detail: heartbeatData
                }));
                
                updateHeartbeatStatus('已发送');
                
            } catch (e) {
                console.log('⚠️ 心跳包发送失败:', e);
                updateHeartbeatStatus('失败');
            }
            
        }, 3000); // 每3秒发送一次心跳包
        
        console.log('💓 学习心跳包发送已启动');
    }
    
    // 启动终极加速
    function startUltimate(speed) {
        stopUltimate();
        
        currentSpeed = speed;
        realStartTime = Date.now();
        accumulatedWatchTime = 0;
        skipCount = 0;
        
        updateTargetSpeed(speed);
        updateBypassStatus('终极运行中');
        updateStudyTimeDisplay('0分钟');
        
        // 启动终极模式
        ultimateMode(speed);
        
        // 启动心跳包
        startHeartbeat();
        
        console.log(`🚀 开始终极加速: ${speed}x`);
    }
    
    // 停止终极加速
    function stopUltimate() {
        if (bypassTimer) {
            clearInterval(bypassTimer);
            bypassTimer = null;
        }
        
        if (heartbeatTimer) {
            clearInterval(heartbeatTimer);
            heartbeatTimer = null;
        }
        
        if (currentVideo) {
            currentVideo.playbackRate = 1;
        }
        
        updateBypassStatus('已停止');
        updateHeartbeatStatus('已停止');
        console.log('⏹️ 终极加速已停止');
    }
    
    // 设置面板事件
    function setupPanelEvents() {
        if (!controlPanel) return;
        
        // 速度按钮
        const speedButtons = controlPanel.querySelectorAll('.speed-btn');
        speedButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const speed = parseFloat(btn.dataset.speed);
                startUltimate(speed);
                
                speedButtons.forEach(b => b.style.background = 'rgba(255,255,255,0.2)');
                btn.style.background = 'rgba(255,255,255,0.5)';
            });
        });
        
        // 开始/停止按钮
        controlPanel.querySelector('#start-ultimate').addEventListener('click', () => {
            startUltimate(currentSpeed > 1 ? currentSpeed : 10);
        });
        
        controlPanel.querySelector('#stop-ultimate').addEventListener('click', stopUltimate);
        
        console.log('✅ 终极版面板事件设置完成');
    }
    
    // 更新显示函数
    function updateTargetSpeed(speed) {
        const el = controlPanel?.querySelector('#target-speed');
        if (el) el.textContent = speed + 'x';
    }
    
    function updateBypassStatus(status) {
        const el = controlPanel?.querySelector('#bypass-status');
        if (el) el.textContent = status;
    }
    
    function updateProgressDisplay(progress) {
        const el = controlPanel?.querySelector('#progress-display');
        if (el) el.textContent = progress;
    }
    
    function updateStudyTimeDisplay(time) {
        const el = controlPanel?.querySelector('#study-time');
        if (el) el.textContent = time;
    }
    
    function updateNetworkStatus(status) {
        const el = controlPanel?.querySelector('#network-status');
        if (el) el.textContent = status;
    }
    
    function updateHeartbeatStatus(status) {
        const el = controlPanel?.querySelector('#heartbeat-status');
        if (el) el.textContent = status;
    }
    
    // 进度同步
    function syncProgress() {
        if (!currentVideo) return;
        
        const currentTime = currentVideo.currentTime;
        const duration = currentVideo.duration;
        const progress = duration ? (currentTime / duration * 100).toFixed(1) + '%' : '0%';
        
        updateProgressDisplay(progress);
    }
    
    // 启动自动同步
    function startAutoSync() {
        setInterval(() => {
            if (currentVideo && !currentVideo.paused) {
                syncProgress();
            }
        }, 500);
    }
    
    // 初始化
    function init() {
        console.log('🔧 开始初始化终极版...');
        
        // 首先设置网络拦截
        setupNetworkInterception();
        updateNetworkStatus('已启用');
        
        createControlPanel();
        
        if (findVideo()) {
            updateBypassStatus('已找到视频');
        } else {
            updateBypassStatus('未找到视频');
        }
        
        setupPanelEvents();
        startAutoSync();
        
        console.log('✅ 终极版初始化完成');
        console.log('💡 使用说明:');
        console.log('   1. 网络请求拦截已启用');
        console.log('   2. 点击速度按钮开始终极加速');
        console.log('   3. 观察"累计学习"时间增长');
        console.log('   4. 心跳包会自动发送学习数据');
    }
    
    // 提供全局访问
    window.yxtUltimateController = {
        start: startUltimate,
        stop: stopUltimate,
        getVideo: () => currentVideo,
        getStudyTime: () => accumulatedWatchTime,
        forceHeartbeat: () => {
            console.log('🔧 强制发送心跳包');
            // 手动触发心跳包
        }
    };
    
    // 启动
    init();
    
    console.log('🎉 终极版加载完成！');
    console.log('🔥 这个版本会深度拦截和修改学习时间数据');
    
})();
