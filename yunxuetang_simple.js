// ==UserScript==
// @name         云学堂视频加速器-简化版
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  云学堂视频播放速度控制，简化版本，兼容性更好
// <AUTHOR>
// @match        *://*.yunxuetang.cn/*
// @match        *://*.yxt.com/*
// @match        *://yunxuetang.cn/*
// @match        *://yxt.com/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';
    
    // 强制输出日志，确保能看到脚本运行
    const log = (msg, data) => {
        console.log(`%c[云学堂加速器] ${msg}`, 'color: #ff6b35; font-weight: bold;', data || '');
    };
    
    log('🚀 脚本开始执行');
    log('当前URL:', window.location.href);
    
    // 全局状态
    let currentVideo = null;
    let controlPanel = null;
    let syncTimer = null;
    
    // 创建控制面板
    function createControlPanel() {
        log('创建控制面板...');
        
        // 移除已存在的面板
        const existing = document.getElementById('yxt-speed-panel-simple');
        if (existing) {
            existing.remove();
            log('移除已存在的面板');
        }
        
        const panel = document.createElement('div');
        panel.id = 'yxt-speed-panel-simple';
        
        // 使用内联样式确保显示
        panel.setAttribute('style', `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            width: 220px !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            padding: 16px !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5) !important;
            z-index: 2147483647 !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif !important;
            font-size: 14px !important;
            border: 2px solid rgba(255,255,255,0.3) !important;
            backdrop-filter: blur(10px) !important;
        `);
        
        panel.innerHTML = `
            <div style="font-weight: 600; font-size: 16px; margin-bottom: 12px; text-align: center;">
                🚀 视频加速器
            </div>
            
            <div style="margin-bottom: 12px;">
                <div style="margin-bottom: 6px; font-weight: 500;">播放速度:</div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 4px;">
                    <button onclick="window.yxtSpeedController.setSpeed(1)" style="padding: 6px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px;">1x</button>
                    <button onclick="window.yxtSpeedController.setSpeed(2)" style="padding: 6px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px;">2x</button>
                    <button onclick="window.yxtSpeedController.setSpeed(3)" style="padding: 6px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px;">3x</button>
                    <button onclick="window.yxtSpeedController.setSpeed(5)" style="padding: 6px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px;">5x</button>
                    <button onclick="window.yxtSpeedController.setSpeed(8)" style="padding: 6px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px;">8x</button>
                    <button onclick="window.yxtSpeedController.setSpeed(10)" style="padding: 6px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px;">10x</button>
                </div>
            </div>
            
            <div style="margin-bottom: 12px;">
                <button onclick="window.yxtSpeedController.syncProgress()" style="width: 100%; padding: 8px; border: none; border-radius: 6px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-weight: 500;">
                    同步进度
                </button>
            </div>
            
            <div style="font-size: 12px; opacity: 0.9; line-height: 1.4;">
                <div>当前速度: <span id="yxt-current-speed-display">1x</span></div>
                <div>视频状态: <span id="yxt-video-status">检测中...</span></div>
                <div>进度: <span id="yxt-progress-display">0%</span></div>
            </div>
            
            <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid rgba(255,255,255,0.2); font-size: 11px; opacity: 0.8;">
                <button onclick="window.yxtSpeedController.togglePanel()" style="background: none; border: none; color: white; cursor: pointer; font-size: 11px;">
                    最小化
                </button>
            </div>
        `;
        
        document.body.appendChild(panel);
        controlPanel = panel;
        log('✅ 控制面板创建成功');
        
        return panel;
    }
    
    // 查找视频元素
    function findVideo() {
        const videos = document.querySelectorAll('video');
        log(`查找视频元素，找到 ${videos.length} 个`);
        
        if (videos.length > 0) {
            currentVideo = videos[0];
            updateVideoStatus('已找到');
            log('✅ 视频元素已找到', currentVideo);
            setupVideoListeners();
            return currentVideo;
        } else {
            updateVideoStatus('未找到');
            log('❌ 未找到视频元素');
            return null;
        }
    }
    
    // 设置视频事件监听
    function setupVideoListeners() {
        if (!currentVideo) return;
        
        currentVideo.addEventListener('loadedmetadata', () => {
            log('视频元数据加载完成');
            updateVideoStatus('已加载');
            syncProgress();
        });
        
        currentVideo.addEventListener('play', () => {
            log('视频开始播放');
            updateVideoStatus('播放中');
            startAutoSync();
        });
        
        currentVideo.addEventListener('pause', () => {
            log('视频暂停');
            updateVideoStatus('已暂停');
            stopAutoSync();
        });
        
        currentVideo.addEventListener('timeupdate', () => {
            updateProgress();
        });
    }
    
    // 更新视频状态显示
    function updateVideoStatus(status) {
        const statusEl = document.getElementById('yxt-video-status');
        if (statusEl) {
            statusEl.textContent = status;
        }
    }
    
    // 更新进度显示
    function updateProgress() {
        if (!currentVideo) return;
        
        const current = currentVideo.currentTime;
        const duration = currentVideo.duration;
        const progress = duration ? (current / duration * 100).toFixed(1) : 0;
        
        const progressEl = document.getElementById('yxt-progress-display');
        if (progressEl) {
            progressEl.textContent = progress + '%';
        }
    }
    
    // 设置播放速度
    function setSpeed(speed) {
        log(`设置播放速度: ${speed}x`);
        
        if (!currentVideo) {
            findVideo();
        }
        
        if (currentVideo) {
            try {
                currentVideo.playbackRate = speed;
                
                // 更新显示
                const speedEl = document.getElementById('yxt-current-speed-display');
                if (speedEl) {
                    speedEl.textContent = speed + 'x';
                }
                
                log(`✅ 播放速度已设置为 ${speed}x`);
                
                // 立即同步一次进度
                setTimeout(syncProgress, 100);
                
            } catch (error) {
                log('❌ 设置播放速度失败', error);
            }
        } else {
            log('❌ 未找到视频元素，无法设置速度');
        }
    }
    
    // 同步进度
    function syncProgress() {
        if (!currentVideo) return;
        
        try {
            const currentTime = currentVideo.currentTime;
            const duration = currentVideo.duration;
            
            // 触发时间更新事件
            const timeUpdateEvent = new Event('timeupdate', { bubbles: true });
            currentVideo.dispatchEvent(timeUpdateEvent);
            
            // 触发进度事件
            const progressEvent = new Event('progress', { bubbles: true });
            currentVideo.dispatchEvent(progressEvent);
            
            log(`同步进度: ${currentTime.toFixed(1)}s / ${duration ? duration.toFixed(1) : '?'}s`);
            
        } catch (error) {
            log('❌ 同步进度失败', error);
        }
    }
    
    // 启动自动同步
    function startAutoSync() {
        stopAutoSync();
        syncTimer = setInterval(syncProgress, 1000);
        log('启动自动同步');
    }
    
    // 停止自动同步
    function stopAutoSync() {
        if (syncTimer) {
            clearInterval(syncTimer);
            syncTimer = null;
            log('停止自动同步');
        }
    }
    
    // 切换面板显示
    function togglePanel() {
        if (!controlPanel) return;
        
        const content = controlPanel.querySelector('div:nth-child(2)');
        if (content) {
            const isHidden = content.style.display === 'none';
            content.style.display = isHidden ? 'block' : 'none';
            log(isHidden ? '展开面板' : '收起面板');
        }
    }
    
    // 创建全局控制器对象
    window.yxtSpeedController = {
        setSpeed: setSpeed,
        syncProgress: syncProgress,
        findVideo: findVideo,
        togglePanel: togglePanel,
        getCurrentVideo: () => currentVideo
    };
    
    // 初始化函数
    function init() {
        log('开始初始化...');
        
        // 创建控制面板
        createControlPanel();
        
        // 查找视频
        findVideo();
        
        // 如果没找到视频，持续尝试
        if (!currentVideo) {
            const findVideoInterval = setInterval(() => {
                if (findVideo()) {
                    clearInterval(findVideoInterval);
                    log('✅ 延迟找到视频元素');
                }
            }, 2000);
            
            // 30秒后停止尝试
            setTimeout(() => {
                clearInterval(findVideoInterval);
                log('⚠️ 查找视频超时');
            }, 30000);
        }
        
        log('✅ 初始化完成');
    }
    
    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
        log('等待DOM加载完成...');
    } else {
        // 延迟执行，确保页面元素都已加载
        setTimeout(init, 1000);
        log('页面已加载，延迟1秒后初始化...');
    }
    
    // 监听页面变化，动态查找视频
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList' && !currentVideo) {
                const videos = document.querySelectorAll('video');
                if (videos.length > 0) {
                    log('🎥 检测到新的视频元素');
                    findVideo();
                }
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    log('🎉 脚本加载完成，等待初始化...');
    
})();
