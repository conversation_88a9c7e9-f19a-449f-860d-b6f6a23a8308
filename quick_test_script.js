// 云学堂快速测试脚本 - 在浏览器控制台直接运行
// 复制此代码到浏览器控制台(F12)并回车执行

(function() {
    'use strict';
    
    console.log('🚀 云学堂快速测试脚本启动');
    
    // 查找视频元素
    function findVideo() {
        const videos = document.querySelectorAll('video');
        if (videos.length > 0) {
            console.log('✅ 找到视频元素:', videos[0]);
            return videos[0];
        }
        console.log('❌ 未找到视频元素');
        return null;
    }
    
    // 创建简单控制器
    function createQuickController(video) {
        // 移除已存在的控制器
        const existing = document.getElementById('quick-speed-controller');
        if (existing) existing.remove();
        
        const controller = document.createElement('div');
        controller.id = 'quick-speed-controller';
        controller.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #000;
            color: #fff;
            padding: 15px;
            border-radius: 8px;
            z-index: 999999;
            font-family: Arial, sans-serif;
            font-size: 14px;
        `;
        
        controller.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold;">⚡ 快速控制</div>
            <div style="margin-bottom: 10px;">
                <button onclick="setSpeed(1)" style="margin: 2px; padding: 5px;">1x</button>
                <button onclick="setSpeed(2)" style="margin: 2px; padding: 5px;">2x</button>
                <button onclick="setSpeed(3)" style="margin: 2px; padding: 5px;">3x</button>
                <button onclick="setSpeed(5)" style="margin: 2px; padding: 5px;">5x</button>
                <button onclick="setSpeed(8)" style="margin: 2px; padding: 5px;">8x</button>
                <button onclick="setSpeed(10)" style="margin: 2px; padding: 5px;">10x</button>
            </div>
            <div style="margin-bottom: 10px;">
                <button onclick="syncNow()" style="padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 4px;">同步进度</button>
            </div>
            <div style="font-size: 12px;">
                当前速度: <span id="current-speed-display">1x</span><br>
                视频时长: <span id="video-duration">--</span><br>
                当前进度: <span id="video-progress">--</span>
            </div>
        `;
        
        document.body.appendChild(controller);
        console.log('✅ 控制器已创建');
    }
    
    // 设置播放速度
    window.setSpeed = function(speed) {
        const video = findVideo();
        if (!video) {
            alert('未找到视频元素！');
            return;
        }
        
        try {
            video.playbackRate = speed;
            document.getElementById('current-speed-display').textContent = speed + 'x';
            console.log(`✅ 播放速度设置为: ${speed}x`);
            
            // 立即同步一次进度
            syncNow();
        } catch (error) {
            console.error('❌ 设置播放速度失败:', error);
            alert('设置播放速度失败: ' + error.message);
        }
    };
    
    // 同步进度
    window.syncNow = function() {
        const video = findVideo();
        if (!video) return;
        
        try {
            const currentTime = video.currentTime;
            const duration = video.duration;
            const progress = duration ? (currentTime / duration * 100).toFixed(1) : 0;
            
            // 更新显示
            document.getElementById('video-duration').textContent = 
                duration ? Math.floor(duration / 60) + ':' + String(Math.floor(duration % 60)).padStart(2, '0') : '--';
            document.getElementById('video-progress').textContent = progress + '%';
            
            // 触发事件
            const events = ['timeupdate', 'progress'];
            events.forEach(eventType => {
                const event = new Event(eventType, { bubbles: true });
                video.dispatchEvent(event);
            });
            
            console.log(`✅ 进度已同步: ${progress}%`);
        } catch (error) {
            console.error('❌ 同步进度失败:', error);
        }
    };
    
    // 自动同步定时器
    let autoSyncTimer = null;
    
    function startAutoSync() {
        if (autoSyncTimer) clearInterval(autoSyncTimer);
        
        autoSyncTimer = setInterval(() => {
            const video = findVideo();
            if (video && !video.paused) {
                syncNow();
            }
        }, 1000); // 每秒同步一次
        
        console.log('✅ 自动同步已启动');
    }
    
    // 监听视频事件
    function setupVideoListeners() {
        const video = findVideo();
        if (!video) return;
        
        video.addEventListener('play', () => {
            console.log('▶️ 视频开始播放');
            startAutoSync();
        });
        
        video.addEventListener('pause', () => {
            console.log('⏸️ 视频暂停');
            if (autoSyncTimer) {
                clearInterval(autoSyncTimer);
                autoSyncTimer = null;
            }
        });
        
        video.addEventListener('loadedmetadata', () => {
            console.log('📊 视频元数据已加载');
            syncNow();
        });
    }
    
    // 主要初始化
    function init() {
        const video = findVideo();
        if (video) {
            createQuickController(video);
            setupVideoListeners();
            
            // 设置默认速度
            setTimeout(() => {
                setSpeed(2); // 默认2倍速
            }, 1000);
            
            console.log('🎉 快速测试脚本初始化完成！');
            console.log('💡 使用方法：');
            console.log('   - 点击按钮设置播放速度');
            console.log('   - 点击"同步进度"手动同步');
            console.log('   - 播放时会自动同步进度');
        } else {
            console.log('⏳ 等待视频加载...');
            setTimeout(init, 2000);
        }
    }
    
    // 启动
    init();
    
    // 提供手动重新初始化的方法
    window.reinitSpeedController = init;
    
    console.log('📝 提示：如果控制器没有出现，请在控制台输入 reinitSpeedController() 重新初始化');
    
})();
