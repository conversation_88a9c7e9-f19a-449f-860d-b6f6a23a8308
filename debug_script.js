// 调试脚本 - 请在浏览器控制台直接运行
// 这个脚本会帮助我们诊断问题

console.log('🔍 开始调试云学堂视频加速脚本...');

// 1. 检查页面基本信息
console.log('📄 页面信息:');
console.log('  URL:', window.location.href);
console.log('  域名:', window.location.hostname);
console.log('  标题:', document.title);
console.log('  readyState:', document.readyState);

// 2. 检查视频元素
console.log('\n🎥 视频元素检查:');
const videos = document.querySelectorAll('video');
console.log('  找到视频数量:', videos.length);
videos.forEach((video, index) => {
    console.log(`  视频 ${index + 1}:`, {
        src: video.src || video.currentSrc,
        duration: video.duration,
        currentTime: video.currentTime,
        playbackRate: video.playbackRate,
        paused: video.paused,
        className: video.className,
        id: video.id
    });
});

// 3. 检查可能的播放器容器
console.log('\n📦 播放器容器检查:');
const playerSelectors = [
    '.video-player',
    '.player-container', 
    '[class*="player"]',
    '[class*="video"]',
    '[id*="player"]',
    '[id*="video"]'
];

playerSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
        console.log(`  ${selector}: 找到 ${elements.length} 个元素`);
        elements.forEach((el, i) => {
            console.log(`    元素 ${i + 1}:`, {
                tagName: el.tagName,
                className: el.className,
                id: el.id,
                hasVideo: el.querySelector('video') ? '有视频' : '无视频'
            });
        });
    }
});

// 4. 检查Vue实例
console.log('\n⚡ Vue实例检查:');
if (window.Vue) {
    console.log('  Vue版本:', window.Vue.version);
    console.log('  Vue实例存在');
} else {
    console.log('  未找到Vue');
}

// 5. 检查云学堂特有对象
console.log('\n☁️ 云学堂对象检查:');
const yxtObjects = ['yxtRPT', 'feConfig', 'yxtcore', 'PAN_GU_PC_PATH'];
yxtObjects.forEach(obj => {
    if (window[obj]) {
        console.log(`  ${obj}:`, typeof window[obj], window[obj]);
    } else {
        console.log(`  ${obj}: 未找到`);
    }
});

// 6. 创建测试控制面板
function createTestPanel() {
    // 移除已存在的面板
    const existing = document.getElementById('debug-test-panel');
    if (existing) existing.remove();
    
    const panel = document.createElement('div');
    panel.id = 'debug-test-panel';
    panel.style.cssText = `
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        background: #ff4444 !important;
        color: white !important;
        padding: 15px !important;
        border-radius: 8px !important;
        z-index: 999999 !important;
        font-family: Arial, sans-serif !important;
        font-size: 14px !important;
        border: 2px solid #fff !important;
        box-shadow: 0 4px 20px rgba(0,0,0,0.5) !important;
        min-width: 200px !important;
    `;
    
    panel.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 10px;">🔍 调试面板</div>
        <div style="margin-bottom: 10px;">
            <button id="find-video-btn" style="padding: 5px; margin: 2px;">查找视频</button>
            <button id="test-speed-btn" style="padding: 5px; margin: 2px;">测试速度</button>
        </div>
        <div style="margin-bottom: 10px;">
            <select id="debug-speed-select" style="width: 100%; padding: 5px;">
                <option value="1">1x</option>
                <option value="2">2x</option>
                <option value="3">3x</option>
                <option value="5">5x</option>
                <option value="10">10x</option>
            </select>
        </div>
        <div style="font-size: 12px;">
            <div>状态: <span id="debug-status">等待</span></div>
            <div>视频: <span id="debug-video-info">未找到</span></div>
        </div>
    `;
    
    document.body.appendChild(panel);
    console.log('✅ 调试面板已创建');
    
    // 绑定事件
    document.getElementById('find-video-btn').onclick = function() {
        const videos = document.querySelectorAll('video');
        const status = document.getElementById('debug-status');
        const videoInfo = document.getElementById('debug-video-info');
        
        if (videos.length > 0) {
            status.textContent = '找到视频';
            videoInfo.textContent = `${videos.length}个视频`;
            console.log('✅ 找到视频:', videos);
            return videos[0];
        } else {
            status.textContent = '未找到';
            videoInfo.textContent = '0个视频';
            console.log('❌ 未找到视频');
            return null;
        }
    };
    
    document.getElementById('test-speed-btn').onclick = function() {
        const video = document.querySelector('video');
        const speed = parseFloat(document.getElementById('debug-speed-select').value);
        const status = document.getElementById('debug-status');
        
        if (video) {
            try {
                video.playbackRate = speed;
                status.textContent = `速度${speed}x`;
                console.log(`✅ 设置速度为 ${speed}x`);
                
                // 触发事件
                video.dispatchEvent(new Event('timeupdate'));
                video.dispatchEvent(new Event('progress'));
                
            } catch (error) {
                status.textContent = '设置失败';
                console.error('❌ 设置速度失败:', error);
            }
        } else {
            status.textContent = '无视频';
            console.log('❌ 没有找到视频元素');
        }
    };
}

// 7. 等待页面加载完成后创建面板
function waitAndCreatePanel() {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createTestPanel);
    } else {
        createTestPanel();
    }
    
    // 也在延迟后再试一次
    setTimeout(createTestPanel, 2000);
    setTimeout(createTestPanel, 5000);
}

// 8. 监听页面变化
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
            const videos = document.querySelectorAll('video');
            if (videos.length > 0) {
                console.log('🎥 检测到新的视频元素:', videos);
            }
        }
    });
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});

// 9. 执行初始化
waitAndCreatePanel();

console.log('\n✅ 调试脚本执行完成');
console.log('📝 请查看右上角是否出现红色调试面板');
console.log('🔧 如果没有出现，请检查页面是否完全加载');

// 10. 提供手动执行函数
window.debugYunxuetang = {
    createPanel: createTestPanel,
    findVideos: () => document.querySelectorAll('video'),
    setSpeed: (speed) => {
        const video = document.querySelector('video');
        if (video) {
            video.playbackRate = speed;
            console.log(`设置速度为 ${speed}x`);
        }
    }
};

console.log('🛠️ 调试工具已添加到 window.debugYunxuetang');
