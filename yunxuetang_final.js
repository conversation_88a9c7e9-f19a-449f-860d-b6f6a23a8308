// 云学堂视频加速器 - 最终修复版
// 直接在浏览器控制台运行

(function() {
    'use strict';
    
    console.log('%c🚀 云学堂视频加速器 - 最终修复版启动', 'color: #ff6b35; font-weight: bold; font-size: 16px;');
    
    // 全局变量
    let currentVideo = null;
    let controlPanel = null;
    let syncTimer = null;
    let currentSpeed = 1;
    
    // 查找视频元素
    function findVideo() {
        const videos = document.querySelectorAll('video');
        console.log(`🔍 找到 ${videos.length} 个视频元素`);
        
        if (videos.length > 0) {
            currentVideo = videos[0];
            console.log('✅ 使用视频元素:', currentVideo);
            console.log('📊 视频信息:', {
                src: currentVideo.src || currentVideo.currentSrc,
                duration: currentVideo.duration,
                currentTime: currentVideo.currentTime,
                playbackRate: currentVideo.playbackRate,
                paused: currentVideo.paused
            });
            return currentVideo;
        }
        return null;
    }
    
    // 创建控制面板
    function createControlPanel() {
        // 移除已存在的面板
        const existing = document.getElementById('yxt-final-panel');
        if (existing) existing.remove();
        
        const panel = document.createElement('div');
        panel.id = 'yxt-final-panel';
        panel.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            width: 260px !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            padding: 16px !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5) !important;
            z-index: 2147483647 !important;
            font-family: Arial, sans-serif !important;
            font-size: 14px !important;
            border: 2px solid rgba(255,255,255,0.3) !important;
        `;
        
        panel.innerHTML = `
            <div style="font-weight: bold; font-size: 16px; margin-bottom: 12px; text-align: center;">
                🚀 视频加速器 (最终版)
            </div>
            
            <div style="margin-bottom: 12px;">
                <div style="margin-bottom: 8px; font-weight: 500;">播放速度控制:</div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 6px; margin-bottom: 8px;">
                    <button class="speed-btn" data-speed="1" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">1x</button>
                    <button class="speed-btn" data-speed="2" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">2x</button>
                    <button class="speed-btn" data-speed="3" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">3x</button>
                    <button class="speed-btn" data-speed="5" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">5x</button>
                    <button class="speed-btn" data-speed="8" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">8x</button>
                    <button class="speed-btn" data-speed="10" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">10x</button>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px;">
                    <button id="sync-btn" style="padding: 8px; border: none; border-radius: 4px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">同步进度</button>
                    <button id="test-btn" style="padding: 8px; border: none; border-radius: 4px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">测试速度</button>
                </div>
            </div>
            
            <div style="font-size: 12px; line-height: 1.4; background: rgba(0,0,0,0.2); padding: 8px; border-radius: 6px;">
                <div>当前速度: <span id="speed-display" style="font-weight: bold; color: #ffeb3b;">1x</span></div>
                <div>视频状态: <span id="status-display" style="font-weight: bold; color: #4caf50;">检测中</span></div>
                <div>播放进度: <span id="progress-display" style="font-weight: bold; color: #2196f3;">0%</span></div>
                <div>实际速率: <span id="rate-display" style="font-weight: bold; color: #ff9800;">--</span></div>
            </div>
            
            <div style="margin-top: 10px; font-size: 11px; opacity: 0.8; text-align: center;">
                最终版 v3.0 | 修复时序问题
            </div>
        `;
        
        document.body.appendChild(panel);
        controlPanel = panel;
        
        console.log('✅ 控制面板创建成功');
        return panel;
    }
    
    // 设置面板事件
    function setupPanelEvents() {
        if (!controlPanel) {
            console.error('❌ 控制面板不存在，无法设置事件');
            return;
        }
        
        // 速度按钮事件
        const speedButtons = controlPanel.querySelectorAll('.speed-btn');
        speedButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const speed = parseFloat(btn.dataset.speed);
                setVideoSpeed(speed);
                
                // 更新按钮样式
                speedButtons.forEach(b => b.style.background = 'rgba(255,255,255,0.2)');
                btn.style.background = 'rgba(255,255,255,0.5)';
            });
            
            // 悬停效果
            btn.addEventListener('mouseenter', () => {
                if (btn.style.background !== 'rgba(255,255,255,0.5)') {
                    btn.style.background = 'rgba(255,255,255,0.3)';
                }
            });
            btn.addEventListener('mouseleave', () => {
                if (btn.style.background !== 'rgba(255,255,255,0.5)') {
                    btn.style.background = 'rgba(255,255,255,0.2)';
                }
            });
        });
        
        // 同步按钮
        const syncBtn = controlPanel.querySelector('#sync-btn');
        if (syncBtn) {
            syncBtn.addEventListener('click', () => {
                syncProgress();
                syncBtn.textContent = '已同步';
                setTimeout(() => syncBtn.textContent = '同步进度', 1000);
            });
        }
        
        // 测试按钮
        const testBtn = controlPanel.querySelector('#test-btn');
        if (testBtn) {
            testBtn.addEventListener('click', () => {
                testVideoSpeed();
            });
        }
        
        console.log('✅ 面板事件设置完成');
    }
    
    // 设置视频播放速度
    function setVideoSpeed(speed) {
        console.log(`🎯 设置播放速度: ${speed}x`);
        
        if (!currentVideo) {
            findVideo();
        }
        
        if (currentVideo) {
            try {
                // 强制设置播放速度
                currentVideo.playbackRate = speed;
                currentSpeed = speed;
                
                // 更新显示
                updateSpeedDisplay(speed);
                updateStatusDisplay('速度已设置');
                
                // 验证设置是否成功
                setTimeout(() => {
                    const actualRate = currentVideo.playbackRate;
                    updateRateDisplay(actualRate);
                    
                    if (Math.abs(actualRate - speed) < 0.1) {
                        console.log(`✅ 播放速度设置成功: ${actualRate}x`);
                    } else {
                        console.log(`⚠️ 播放速度设置异常: 期望${speed}x, 实际${actualRate}x`);
                        
                        // 如果设置失败，尝试强制设置
                        try {
                            Object.defineProperty(currentVideo, 'playbackRate', {
                                value: speed,
                                writable: true
                            });
                            console.log('🔧 尝试强制设置播放速度');
                        } catch (e) {
                            console.log('❌ 强制设置也失败了');
                        }
                    }
                }, 100);
                
                // 立即同步进度
                syncProgress();
                
            } catch (error) {
                console.error('❌ 设置播放速度失败:', error);
                updateStatusDisplay('设置失败');
            }
        } else {
            console.error('❌ 未找到视频元素');
            updateStatusDisplay('无视频');
        }
    }
    
    // 测试视频速度
    function testVideoSpeed() {
        if (!currentVideo) {
            console.log('❌ 无视频可测试');
            updateStatusDisplay('无视频');
            return;
        }
        
        console.log('🧪 开始测试视频速度...');
        updateStatusDisplay('测试中...');
        
        const startTime = currentVideo.currentTime;
        const startTimestamp = Date.now();
        
        setTimeout(() => {
            const endTime = currentVideo.currentTime;
            const endTimestamp = Date.now();
            
            const videoTimeDiff = endTime - startTime;
            const realTimeDiff = (endTimestamp - startTimestamp) / 1000;
            
            const actualSpeed = realTimeDiff > 0 ? videoTimeDiff / realTimeDiff : 0;
            
            console.log('📊 速度测试结果:', {
                设置速度: currentSpeed + 'x',
                实际速度: actualSpeed.toFixed(2) + 'x',
                视频时间差: videoTimeDiff.toFixed(2) + 's',
                真实时间差: realTimeDiff.toFixed(2) + 's',
                playbackRate: currentVideo.playbackRate
            });
            
            updateRateDisplay(actualSpeed.toFixed(2) + 'x');
            
            if (actualSpeed > 1.1) {
                updateStatusDisplay('加速正常');
                console.log('✅ 视频加速正常工作');
            } else if (actualSpeed < 0.1) {
                updateStatusDisplay('视频暂停');
                console.log('⏸️ 视频可能处于暂停状态');
            } else {
                updateStatusDisplay('加速异常');
                console.log('⚠️ 视频加速可能未生效');
            }
            
        }, 2000); // 2秒后检测
    }
    
    // 同步进度
    function syncProgress() {
        if (!currentVideo) return;
        
        try {
            const currentTime = currentVideo.currentTime;
            const duration = currentVideo.duration;
            const progress = duration ? (currentTime / duration * 100).toFixed(1) : 0;
            
            // 更新进度显示
            updateProgressDisplay(progress + '%');
            
            // 触发事件
            const events = ['timeupdate', 'progress', 'seeking', 'seeked'];
            events.forEach(eventType => {
                const event = new Event(eventType, { bubbles: true });
                currentVideo.dispatchEvent(event);
            });
            
            // 静默日志，避免刷屏
            // console.log(`🔄 同步进度: ${progress}%`);
            
        } catch (error) {
            console.error('❌ 同步进度失败:', error);
        }
    }
    
    // 安全的更新显示函数
    function updateSpeedDisplay(speed) {
        if (!controlPanel) return;
        const el = controlPanel.querySelector('#speed-display');
        if (el) el.textContent = speed + 'x';
    }
    
    function updateStatusDisplay(status) {
        if (!controlPanel) return;
        const el = controlPanel.querySelector('#status-display');
        if (el) el.textContent = status;
    }
    
    function updateProgressDisplay(progress) {
        if (!controlPanel) return;
        const el = controlPanel.querySelector('#progress-display');
        if (el) el.textContent = progress;
    }
    
    function updateRateDisplay(rate) {
        if (!controlPanel) return;
        const el = controlPanel.querySelector('#rate-display');
        if (el) el.textContent = rate;
    }
    
    // 启动自动同步
    function startAutoSync() {
        if (syncTimer) clearInterval(syncTimer);
        syncTimer = setInterval(() => {
            if (currentVideo && !currentVideo.paused) {
                syncProgress();
            }
        }, 1000);
        console.log('🔄 自动同步已启动');
    }
    
    // 设置视频事件监听
    function setupVideoListeners() {
        if (!currentVideo) return;
        
        currentVideo.addEventListener('play', () => {
            console.log('▶️ 视频开始播放');
            updateStatusDisplay('播放中');
            startAutoSync();
        });
        
        currentVideo.addEventListener('pause', () => {
            console.log('⏸️ 视频暂停');
            updateStatusDisplay('已暂停');
            if (syncTimer) {
                clearInterval(syncTimer);
                syncTimer = null;
            }
        });
        
        currentVideo.addEventListener('loadedmetadata', () => {
            console.log('📊 视频元数据加载完成');
            updateStatusDisplay('已加载');
        });
        
        // 监听播放速度变化
        currentVideo.addEventListener('ratechange', () => {
            const rate = currentVideo.playbackRate;
            console.log(`🔄 播放速度变化: ${rate}x`);
            updateSpeedDisplay(rate);
            updateRateDisplay(rate + 'x');
        });
    }
    
    // 初始化
    function init() {
        console.log('🔧 开始初始化最终版...');
        
        // 先创建控制面板
        createControlPanel();
        
        // 再查找视频并设置状态
        if (findVideo()) {
            setupVideoListeners();
            updateStatusDisplay('已找到');
            
            // 显示当前播放速度
            updateSpeedDisplay(currentVideo.playbackRate);
            updateRateDisplay(currentVideo.playbackRate + 'x');
        } else {
            updateStatusDisplay('未找到');
        }
        
        // 设置面板事件
        setupPanelEvents();
        
        // 启动自动同步
        startAutoSync();
        
        console.log('✅ 最终版初始化完成');
        console.log('💡 使用说明:');
        console.log('   1. 点击速度按钮设置播放速度');
        console.log('   2. 点击"测试速度"验证加速效果');
        console.log('   3. 观察"实际速率"显示');
        console.log('   4. 如果速度无效，可能被播放器重置');
    }
    
    // 提供全局访问
    window.yxtFinalController = {
        setSpeed: setVideoSpeed,
        testSpeed: testVideoSpeed,
        sync: syncProgress,
        findVideo: findVideo,
        getVideo: () => currentVideo,
        forceSpeed: (speed) => {
            // 强制设置速度的备用方法
            if (currentVideo) {
                currentVideo.playbackRate = speed;
                console.log(`🔧 强制设置速度: ${speed}x`);
            }
        }
    };
    
    // 启动
    init();
    
    console.log('🎉 最终版加载完成！');
    console.log('🛠️ 如果速度设置无效，请尝试: yxtFinalController.forceSpeed(5)');
    
})();
