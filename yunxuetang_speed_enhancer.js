// 云学堂视频播放速度增强脚本
// 用于解决前端播放速度与服务器进度同步问题

(function() {
    'use strict';
    
    console.log('云学堂视频速度增强脚本已加载');
    
    // 配置选项
    const CONFIG = {
        maxSpeed: 10,           // 最大播放速度
        progressSyncInterval: 1000,  // 进度同步间隔(毫秒)
        heartbeatInterval: 5000,     // 心跳间隔(毫秒)
        debug: true             // 调试模式
    };
    
    // 全局变量
    let videoElement = null;
    let originalPlaybackRate = 1;
    let progressTimer = null;
    let heartbeatTimer = null;
    let lastReportedTime = 0;
    let actualWatchTime = 0;
    let startTime = Date.now();
    
    // 调试日志函数
    function debugLog(message, data = null) {
        if (CONFIG.debug) {
            console.log(`[云学堂加速器] ${message}`, data || '');
        }
    }
    
    // 查找视频元素
    function findVideoElement() {
        const videos = document.querySelectorAll('video');
        if (videos.length > 0) {
            videoElement = videos[0];
            debugLog('找到视频元素', videoElement);
            return true;
        }
        return false;
    }
    
    // 创建速度控制面板
    function createSpeedPanel() {
        if (document.getElementById('speed-enhancer-panel')) return;
        
        const panel = document.createElement('div');
        panel.id = 'speed-enhancer-panel';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            min-width: 200px;
        `;
        
        panel.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold;">视频速度控制</div>
            <div style="margin-bottom: 10px;">
                <label>播放速度: </label>
                <select id="speed-selector" style="margin-left: 5px;">
                    <option value="1">1x (正常)</option>
                    <option value="1.5">1.5x</option>
                    <option value="2">2x</option>
                    <option value="3">3x</option>
                    <option value="5">5x</option>
                    <option value="8">8x</option>
                    <option value="10">10x</option>
                </select>
            </div>
            <div style="margin-bottom: 10px;">
                <button id="sync-progress" style="padding: 5px 10px;">手动同步进度</button>
            </div>
            <div style="font-size: 12px; opacity: 0.8;">
                <div>当前速度: <span id="current-speed">1x</span></div>
                <div>实际观看: <span id="actual-time">0</span>秒</div>
                <div>报告进度: <span id="reported-time">0</span>秒</div>
            </div>
        `;
        
        document.body.appendChild(panel);
        
        // 绑定事件
        document.getElementById('speed-selector').addEventListener('change', function(e) {
            const speed = parseFloat(e.target.value);
            setVideoSpeed(speed);
        });
        
        document.getElementById('sync-progress').addEventListener('click', function() {
            syncProgressToServer();
        });
    }
    
    // 设置视频播放速度
    function setVideoSpeed(speed) {
        if (!videoElement) return;
        
        videoElement.playbackRate = speed;
        document.getElementById('current-speed').textContent = speed + 'x';
        debugLog(`设置播放速度为: ${speed}x`);
        
        // 调整同步频率
        if (speed > 2) {
            CONFIG.progressSyncInterval = 500; // 高速播放时更频繁同步
        } else {
            CONFIG.progressSyncInterval = 1000;
        }
        
        restartProgressSync();
    }
    
    // 计算实际观看时间
    function calculateActualWatchTime() {
        if (!videoElement) return 0;
        
        const currentTime = Date.now();
        const elapsed = (currentTime - startTime) / 1000;
        const speed = videoElement.playbackRate || 1;
        
        // 实际观看时间 = 经过时间 / 播放速度
        actualWatchTime = elapsed / speed;
        return actualWatchTime;
    }
    
    // 模拟正常播放进度
    function simulateNormalProgress() {
        if (!videoElement) return;
        
        const actualTime = calculateActualWatchTime();
        const videoCurrentTime = videoElement.currentTime;
        
        // 更新显示
        document.getElementById('actual-time').textContent = Math.floor(actualTime);
        document.getElementById('reported-time').textContent = Math.floor(videoCurrentTime);
        
        // 如果视频进度超前太多，需要调整报告的进度
        if (videoCurrentTime - lastReportedTime > 2) {
            syncProgressToServer();
        }
    }
    
    // 同步进度到服务器
    function syncProgressToServer() {
        if (!videoElement) return;
        
        const currentProgress = videoElement.currentTime;
        const duration = videoElement.duration;
        
        debugLog('同步进度到服务器', {
            currentTime: currentProgress,
            duration: duration,
            percentage: (currentProgress / duration * 100).toFixed(2) + '%'
        });
        
        // 尝试触发原生的进度报告事件
        try {
            // 模拟正常的时间更新事件
            const timeUpdateEvent = new Event('timeupdate');
            videoElement.dispatchEvent(timeUpdateEvent);
            
            // 模拟进度事件
            const progressEvent = new Event('progress');
            videoElement.dispatchEvent(progressEvent);
            
            lastReportedTime = currentProgress;
            
        } catch (error) {
            debugLog('同步进度时出错', error);
        }
    }
    
    // 拦截和修改网络请求
    function interceptNetworkRequests() {
        // 保存原始的fetch和XMLHttpRequest
        const originalFetch = window.fetch;
        const originalXHR = window.XMLHttpRequest;
        
        // 拦截fetch请求
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && (url.includes('progress') || url.includes('heartbeat'))) {
                debugLog('拦截到进度相关请求', url);
            }
            return originalFetch.apply(this, args);
        };
        
        // 拦截XMLHttpRequest
        const XHROpen = originalXHR.prototype.open;
        originalXHR.prototype.open = function(method, url, ...rest) {
            if (url.includes('progress') || url.includes('heartbeat')) {
                debugLog('拦截到XHR进度请求', url);
            }
            return XHROpen.apply(this, [method, url, ...rest]);
        };
    }
    
    // 启动进度同步
    function startProgressSync() {
        if (progressTimer) clearInterval(progressTimer);
        
        progressTimer = setInterval(() => {
            simulateNormalProgress();
        }, CONFIG.progressSyncInterval);
        
        debugLog('启动进度同步定时器');
    }
    
    // 重启进度同步
    function restartProgressSync() {
        if (progressTimer) clearInterval(progressTimer);
        startProgressSync();
    }
    
    // 启动心跳
    function startHeartbeat() {
        if (heartbeatTimer) clearInterval(heartbeatTimer);
        
        heartbeatTimer = setInterval(() => {
            syncProgressToServer();
        }, CONFIG.heartbeatInterval);
        
        debugLog('启动心跳定时器');
    }
    
    // 初始化脚本
    function init() {
        debugLog('初始化云学堂视频速度增强脚本');
        
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }
        
        // 查找视频元素
        const checkVideo = setInterval(() => {
            if (findVideoElement()) {
                clearInterval(checkVideo);
                
                // 创建控制面板
                createSpeedPanel();
                
                // 启动监控
                startProgressSync();
                startHeartbeat();
                
                // 拦截网络请求
                interceptNetworkRequests();
                
                debugLog('脚本初始化完成');
            }
        }, 1000);
        
        // 10秒后停止查找
        setTimeout(() => {
            clearInterval(checkVideo);
        }, 10000);
    }
    
    // 启动脚本
    init();
    
})();
