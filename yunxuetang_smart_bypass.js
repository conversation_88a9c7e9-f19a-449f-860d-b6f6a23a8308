// 云学堂视频加速器 - 智能绕过版
// 解决服务器端学习时间同步问题

(function() {
    'use strict';
    
    console.log('%c🚀 云学堂视频加速器 - 智能绕过版启动', 'color: #ff6b35; font-weight: bold; font-size: 16px;');
    
    // 全局变量
    let currentVideo = null;
    let controlPanel = null;
    let bypassTimer = null;
    let syncTimer = null;
    let currentSpeed = 1;
    let bypassMethod = 'smart'; // smart, frame, seek, hybrid
    
    // 学习时间追踪
    let realStartTime = 0;
    let virtualWatchTime = 0;
    let lastSyncTime = 0;
    let skipCount = 0;
    
    // 查找视频元素
    function findVideo() {
        const videos = document.querySelectorAll('video');
        console.log(`🔍 找到 ${videos.length} 个视频元素`);
        
        if (videos.length > 0) {
            currentVideo = videos[0];
            console.log('✅ 使用视频元素:', currentVideo);
            return currentVideo;
        }
        return null;
    }
    
    // 创建控制面板
    function createControlPanel() {
        const existing = document.getElementById('yxt-smart-panel');
        if (existing) existing.remove();
        
        const panel = document.createElement('div');
        panel.id = 'yxt-smart-panel';
        panel.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            width: 300px !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            padding: 16px !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5) !important;
            z-index: 2147483647 !important;
            font-family: Arial, sans-serif !important;
            font-size: 14px !important;
            border: 2px solid rgba(255,255,255,0.3) !important;
        `;
        
        panel.innerHTML = `
            <div style="font-weight: bold; font-size: 16px; margin-bottom: 12px; text-align: center;">
                🧠 智能视频加速器
            </div>
            
            <div style="margin-bottom: 12px;">
                <div style="margin-bottom: 8px; font-weight: 500;">加速模式:</div>
                <select id="bypass-method" style="width: 100%; padding: 6px; border: none; border-radius: 4px; background: rgba(255,255,255,0.9); color: #333;">
                    <option value="smart">智能模式 (推荐)</option>
                    <option value="hybrid">混合模式</option>
                    <option value="seek">时间跳跃模式</option>
                    <option value="frame">帧跳跃模式</option>
                </select>
            </div>
            
            <div style="margin-bottom: 12px;">
                <div style="margin-bottom: 8px; font-weight: 500;">播放速度:</div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 6px; margin-bottom: 8px;">
                    <button class="speed-btn" data-speed="3" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">3x</button>
                    <button class="speed-btn" data-speed="5" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">5x</button>
                    <button class="speed-btn" data-speed="8" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">8x</button>
                    <button class="speed-btn" data-speed="10" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">10x</button>
                    <button class="speed-btn" data-speed="15" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">15x</button>
                    <button class="speed-btn" data-speed="20" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">20x</button>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px;">
                    <button id="start-bypass" style="padding: 8px; border: none; border-radius: 4px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">开始加速</button>
                    <button id="stop-bypass" style="padding: 8px; border: none; border-radius: 4px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">停止加速</button>
                </div>
            </div>
            
            <div style="font-size: 12px; line-height: 1.4; background: rgba(0,0,0,0.2); padding: 8px; border-radius: 6px;">
                <div>目标速度: <span id="target-speed" style="font-weight: bold; color: #ffeb3b;">1x</span></div>
                <div>加速状态: <span id="bypass-status" style="font-weight: bold; color: #4caf50;">待机</span></div>
                <div>播放进度: <span id="progress-display" style="font-weight: bold; color: #2196f3;">0%</span></div>
                <div>虚拟观看: <span id="virtual-time" style="font-weight: bold; color: #ff9800;">0分钟</span></div>
                <div>学习同步: <span id="sync-status" style="font-weight: bold; color: #9c27b0;">正常</span></div>
            </div>
            
            <div style="margin-top: 10px; font-size: 11px; opacity: 0.8; text-align: center;">
                智能版 v5.0 | 解决学习时间同步
            </div>
        `;
        
        document.body.appendChild(panel);
        controlPanel = panel;
        
        console.log('✅ 智能版控制面板创建成功');
        return panel;
    }
    
    // 智能模式 - 平衡速度和学习时间记录
    function smartMode(targetSpeed) {
        if (bypassTimer) clearInterval(bypassTimer);
        
        // 计算智能参数
        const basePlaybackRate = Math.min(targetSpeed, 2); // 基础播放速度不超过2x
        const skipRatio = targetSpeed > 2 ? (targetSpeed - basePlaybackRate) / targetSpeed : 0;
        const skipInterval = 1000; // 每秒检查一次
        
        // 设置基础播放速度
        currentVideo.playbackRate = basePlaybackRate;
        
        console.log(`🧠 智能模式参数: 基础速度${basePlaybackRate}x, 跳跃比例${(skipRatio * 100).toFixed(1)}%`);
        
        bypassTimer = setInterval(() => {
            if (!currentVideo || currentVideo.paused) return;
            
            const currentTime = currentVideo.currentTime;
            const duration = currentVideo.duration;
            
            // 根据跳跃比例决定是否跳跃
            if (Math.random() < skipRatio && currentTime < duration - 5) {
                const skipAmount = Math.min(targetSpeed - basePlaybackRate, duration - currentTime - 1);
                const newTime = currentTime + skipAmount;
                
                currentVideo.currentTime = newTime;
                skipCount++;
                updateSkipCount(skipCount);
                
                console.log(`⏭️ 智能跳跃: ${currentTime.toFixed(1)}s -> ${newTime.toFixed(1)}s`);
                
                // 触发进度事件
                triggerProgressEvents();
            }
            
            // 更新虚拟观看时间（按正常速度计算）
            updateVirtualWatchTime();
            
        }, skipInterval);
        
        console.log(`🎯 智能模式启动: ${targetSpeed}x`);
    }
    
    // 混合模式 - 结合播放速度和时间跳跃
    function hybridMode(targetSpeed) {
        if (bypassTimer) clearInterval(bypassTimer);
        
        // 设置最大可能的播放速度
        currentVideo.playbackRate = Math.min(targetSpeed, 2);
        
        // 如果目标速度超过2倍，使用时间跳跃补充
        if (targetSpeed > 2) {
            const remainingSpeed = targetSpeed - 2;
            const seekInterval = 1000;
            const seekAmount = remainingSpeed;
            
            bypassTimer = setInterval(() => {
                if (!currentVideo || currentVideo.paused) return;
                
                const currentTime = currentVideo.currentTime;
                const newTime = Math.min(currentTime + seekAmount, currentVideo.duration - 1);
                
                if (newTime > currentTime) {
                    currentVideo.currentTime = newTime;
                    skipCount++;
                    updateSkipCount(skipCount);
                    triggerProgressEvents();
                }
                
                updateVirtualWatchTime();
                
            }, seekInterval);
        }
        
        console.log(`🎯 混合模式启动: 播放速度${Math.min(targetSpeed, 2)}x + 时间跳跃`);
    }
    
    // 时间跳跃模式
    function timeSeekMode(targetSpeed) {
        if (bypassTimer) clearInterval(bypassTimer);
        
        const seekInterval = 1000;
        const seekAmount = targetSpeed - 1;
        
        bypassTimer = setInterval(() => {
            if (!currentVideo || currentVideo.paused) return;
            
            const currentTime = currentVideo.currentTime;
            const newTime = Math.min(currentTime + seekAmount, currentVideo.duration - 1);
            
            if (newTime > currentTime) {
                currentVideo.currentTime = newTime;
                skipCount++;
                updateSkipCount(skipCount);
                triggerProgressEvents();
            }
            
            updateVirtualWatchTime();
            
        }, seekInterval);
        
        console.log(`🎯 时间跳跃模式启动: ${targetSpeed}x`);
    }
    
    // 帧跳跃模式
    function frameSkipMode(targetSpeed) {
        if (bypassTimer) clearInterval(bypassTimer);
        
        const skipInterval = Math.max(50, 1000 / targetSpeed);
        const skipAmount = (targetSpeed - 1) * 0.1;
        
        bypassTimer = setInterval(() => {
            if (!currentVideo || currentVideo.paused) return;
            
            const currentTime = currentVideo.currentTime;
            const newTime = Math.min(currentTime + skipAmount, currentVideo.duration - 1);
            
            if (newTime > currentTime) {
                currentVideo.currentTime = newTime;
                skipCount++;
                updateSkipCount(skipCount);
                triggerProgressEvents();
            }
            
            updateVirtualWatchTime();
            
        }, skipInterval);
        
        console.log(`🎯 帧跳跃模式启动: ${targetSpeed}x`);
    }
    
    // 触发进度事件
    function triggerProgressEvents() {
        if (!currentVideo) return;
        
        const events = ['timeupdate', 'progress', 'seeking', 'seeked'];
        events.forEach(eventType => {
            const event = new Event(eventType, { bubbles: true });
            currentVideo.dispatchEvent(event);
        });
        
        // 尝试触发云学堂特有的事件
        try {
            // 触发可能的Vue组件事件
            if (window.Vue) {
                const vueElements = document.querySelectorAll('[data-v-*]');
                vueElements.forEach(el => {
                    if (el.__vue__ && el.__vue__.$emit) {
                        el.__vue__.$emit('timeupdate');
                        el.__vue__.$emit('progress');
                    }
                });
            }
            
            // 触发自定义事件
            window.dispatchEvent(new CustomEvent('video-progress-update', {
                detail: {
                    currentTime: currentVideo.currentTime,
                    duration: currentVideo.duration,
                    virtualWatchTime: virtualWatchTime
                }
            }));
            
        } catch (e) {
            // 忽略错误
        }
    }
    
    // 更新虚拟观看时间
    function updateVirtualWatchTime() {
        const now = Date.now();
        if (realStartTime > 0) {
            // 按正常速度计算观看时间，而不是加速时间
            const realElapsed = (now - realStartTime) / 1000;
            virtualWatchTime = realElapsed;
            
            const minutes = Math.floor(virtualWatchTime / 60);
            const seconds = Math.floor(virtualWatchTime % 60);
            updateVirtualTimeDisplay(`${minutes}分${seconds}秒`);
        }
    }
    
    // 启动智能学习时间同步
    function startSmartSync() {
        if (syncTimer) clearInterval(syncTimer);
        
        syncTimer = setInterval(() => {
            if (!currentVideo || currentVideo.paused) return;
            
            // 强制触发学习时间更新
            triggerProgressEvents();
            
            // 模拟正常的心跳包
            try {
                // 尝试调用可能的学习时间更新API
                if (window.fetch) {
                    // 这里可以添加具体的API调用
                    console.log('📡 发送学习时间同步信号');
                }
            } catch (e) {
                // 忽略错误
            }
            
            updateSyncStatus('同步中');
            
        }, 5000); // 每5秒同步一次
        
        console.log('🔄 智能学习时间同步已启动');
    }
    
    // 启动绕过加速
    function startBypass(speed) {
        stopBypass();
        
        currentSpeed = speed;
        realStartTime = Date.now();
        virtualWatchTime = 0;
        skipCount = 0;
        
        updateTargetSpeed(speed);
        updateBypassStatus('运行中');
        updateSkipCount(0);
        updateSyncStatus('启动中');
        
        // 根据选择的方法启动
        switch (bypassMethod) {
            case 'smart':
                smartMode(speed);
                break;
            case 'hybrid':
                hybridMode(speed);
                break;
            case 'seek':
                timeSeekMode(speed);
                break;
            case 'frame':
                frameSkipMode(speed);
                break;
        }
        
        // 启动智能同步
        startSmartSync();
        
        console.log(`🚀 开始智能加速: ${speed}x, 方法: ${bypassMethod}`);
    }
    
    // 停止绕过加速
    function stopBypass() {
        if (bypassTimer) {
            clearInterval(bypassTimer);
            bypassTimer = null;
        }
        
        if (syncTimer) {
            clearInterval(syncTimer);
            syncTimer = null;
        }
        
        if (currentVideo) {
            currentVideo.playbackRate = 1;
        }
        
        updateBypassStatus('已停止');
        updateSyncStatus('已停止');
        console.log('⏹️ 智能加速已停止');
    }
    
    // 设置面板事件
    function setupPanelEvents() {
        if (!controlPanel) return;
        
        // 方法选择
        const methodSelect = controlPanel.querySelector('#bypass-method');
        methodSelect.addEventListener('change', (e) => {
            bypassMethod = e.target.value;
            console.log(`🔧 切换加速方法: ${bypassMethod}`);
        });
        
        // 速度按钮
        const speedButtons = controlPanel.querySelectorAll('.speed-btn');
        speedButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const speed = parseFloat(btn.dataset.speed);
                startBypass(speed);
                
                speedButtons.forEach(b => b.style.background = 'rgba(255,255,255,0.2)');
                btn.style.background = 'rgba(255,255,255,0.5)';
            });
        });
        
        // 开始/停止按钮
        controlPanel.querySelector('#start-bypass').addEventListener('click', () => {
            startBypass(currentSpeed > 1 ? currentSpeed : 5);
        });
        
        controlPanel.querySelector('#stop-bypass').addEventListener('click', stopBypass);
        
        console.log('✅ 智能版面板事件设置完成');
    }
    
    // 更新显示函数
    function updateTargetSpeed(speed) {
        const el = controlPanel?.querySelector('#target-speed');
        if (el) el.textContent = speed + 'x';
    }
    
    function updateBypassStatus(status) {
        const el = controlPanel?.querySelector('#bypass-status');
        if (el) el.textContent = status;
    }
    
    function updateProgressDisplay(progress) {
        const el = controlPanel?.querySelector('#progress-display');
        if (el) el.textContent = progress;
    }
    
    function updateSkipCount(count) {
        const el = controlPanel?.querySelector('#skip-count');
        if (el) el.textContent = count;
    }
    
    function updateVirtualTimeDisplay(time) {
        const el = controlPanel?.querySelector('#virtual-time');
        if (el) el.textContent = time;
    }
    
    function updateSyncStatus(status) {
        const el = controlPanel?.querySelector('#sync-status');
        if (el) el.textContent = status;
    }
    
    // 进度同步
    function syncProgress() {
        if (!currentVideo) return;
        
        const currentTime = currentVideo.currentTime;
        const duration = currentVideo.duration;
        const progress = duration ? (currentTime / duration * 100).toFixed(1) + '%' : '0%';
        
        updateProgressDisplay(progress);
    }
    
    // 启动自动同步
    function startAutoSync() {
        setInterval(() => {
            if (currentVideo && !currentVideo.paused) {
                syncProgress();
            }
        }, 500);
    }
    
    // 初始化
    function init() {
        console.log('🔧 开始初始化智能版...');
        
        createControlPanel();
        
        if (findVideo()) {
            updateBypassStatus('已找到视频');
        } else {
            updateBypassStatus('未找到视频');
        }
        
        setupPanelEvents();
        startAutoSync();
        
        console.log('✅ 智能版初始化完成');
        console.log('💡 使用说明:');
        console.log('   1. 推荐使用"智能模式"');
        console.log('   2. 点击速度按钮开始加速');
        console.log('   3. 观察"虚拟观看"时间增长');
        console.log('   4. "学习同步"显示服务器同步状态');
    }
    
    // 提供全局访问
    window.yxtSmartController = {
        start: startBypass,
        stop: stopBypass,
        setMethod: (method) => {
            bypassMethod = method;
            console.log(`🔧 设置加速方法: ${method}`);
        },
        getVideo: () => currentVideo,
        getVirtualTime: () => virtualWatchTime
    };
    
    // 启动
    init();
    
    console.log('🎉 智能版加载完成！');
    console.log('🧠 这个版本会智能处理学习时间同步问题');
    
})();
