// ==UserScript==
// @name         云学堂视频播放速度增强器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  突破云学堂视频播放速度限制，支持最高10倍速播放并正确同步学习进度
// <AUTHOR>
// @match        https://*.yunxuetang.cn/*
// @match        https://*.yxt.com/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🚀 云学堂视频速度增强器已启动');
    
    // 配置
    const CONFIG = {
        speeds: [1, 1.5, 2, 3, 5, 8, 10, 16],
        defaultSpeed: 2,
        syncInterval: 800,
        heartbeatInterval: 3000,
        debug: true
    };
    
    // 状态管理
    const state = {
        videoElement: null,
        currentSpeed: 1,
        isPlaying: false,
        startTime: 0,
        lastSyncTime: 0,
        totalWatchTime: 0,
        timers: {
            sync: null,
            heartbeat: null,
            detection: null
        }
    };
    
    // 工具函数
    const utils = {
        log: (msg, data) => CONFIG.debug && console.log(`[云学堂加速器] ${msg}`, data || ''),
        
        waitForElement: (selector, timeout = 10000) => {
            return new Promise((resolve, reject) => {
                const element = document.querySelector(selector);
                if (element) return resolve(element);
                
                const observer = new MutationObserver(() => {
                    const element = document.querySelector(selector);
                    if (element) {
                        observer.disconnect();
                        resolve(element);
                    }
                });
                
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
                
                setTimeout(() => {
                    observer.disconnect();
                    reject(new Error('Element not found'));
                }, timeout);
            });
        },
        
        injectCSS: (css) => {
            const style = document.createElement('style');
            style.textContent = css;
            document.head.appendChild(style);
        }
    };
    
    // 创建控制界面
    function createControlPanel() {
        const panelHTML = `
            <div id="yxt-speed-panel" style="
                position: fixed;
                top: 80px;
                right: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 16px;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                z-index: 999999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                min-width: 240px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.2);
            ">
                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                    <span style="font-weight: 600; font-size: 16px;">🚀 播放控制</span>
                    <button id="yxt-panel-toggle" style="
                        margin-left: auto;
                        background: none;
                        border: none;
                        color: white;
                        cursor: pointer;
                        font-size: 18px;
                    ">−</button>
                </div>
                
                <div id="yxt-panel-content">
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 6px; font-weight: 500;">播放速度:</label>
                        <select id="yxt-speed-select" style="
                            width: 100%;
                            padding: 8px;
                            border: none;
                            border-radius: 6px;
                            background: rgba(255,255,255,0.9);
                            color: #333;
                            font-size: 14px;
                        ">
                            ${CONFIG.speeds.map(speed => 
                                `<option value="${speed}" ${speed === CONFIG.defaultSpeed ? 'selected' : ''}>${speed}x ${speed === 1 ? '(原速)' : speed >= 5 ? '(极速)' : '(快速)'}</option>`
                            ).join('')}
                        </select>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 12px;">
                        <button id="yxt-sync-btn" style="
                            padding: 8px;
                            border: none;
                            border-radius: 6px;
                            background: rgba(255,255,255,0.2);
                            color: white;
                            cursor: pointer;
                            font-size: 12px;
                            transition: all 0.2s;
                        ">同步进度</button>
                        <button id="yxt-reset-btn" style="
                            padding: 8px;
                            border: none;
                            border-radius: 6px;
                            background: rgba(255,255,255,0.2);
                            color: white;
                            cursor: pointer;
                            font-size: 12px;
                            transition: all 0.2s;
                        ">重置速度</button>
                    </div>
                    
                    <div style="font-size: 12px; opacity: 0.9; line-height: 1.4;">
                        <div>当前: <span id="yxt-current-speed">1x</span></div>
                        <div>状态: <span id="yxt-status">等待视频</span></div>
                        <div>进度: <span id="yxt-progress">0%</span></div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', panelHTML);
        
        // 绑定事件
        const speedSelect = document.getElementById('yxt-speed-select');
        const syncBtn = document.getElementById('yxt-sync-btn');
        const resetBtn = document.getElementById('yxt-reset-btn');
        const toggleBtn = document.getElementById('yxt-panel-toggle');
        const content = document.getElementById('yxt-panel-content');
        
        speedSelect.addEventListener('change', (e) => {
            const speed = parseFloat(e.target.value);
            setVideoSpeed(speed);
        });
        
        syncBtn.addEventListener('click', () => {
            syncProgress();
            syncBtn.textContent = '已同步';
            setTimeout(() => syncBtn.textContent = '同步进度', 1000);
        });
        
        resetBtn.addEventListener('click', () => {
            setVideoSpeed(1);
            speedSelect.value = '1';
        });
        
        toggleBtn.addEventListener('click', () => {
            const isHidden = content.style.display === 'none';
            content.style.display = isHidden ? 'block' : 'none';
            toggleBtn.textContent = isHidden ? '−' : '+';
        });
        
        // 添加悬停效果
        [syncBtn, resetBtn].forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.background = 'rgba(255,255,255,0.3)';
            });
            btn.addEventListener('mouseleave', () => {
                btn.style.background = 'rgba(255,255,255,0.2)';
            });
        });
    }
    
    // 查找并初始化视频
    async function initVideo() {
        try {
            // 多种选择器尝试找到视频元素
            const selectors = [
                'video',
                '.video-player video',
                '.player-container video',
                '[class*="video"] video',
                '[id*="video"] video'
            ];
            
            let video = null;
            for (const selector of selectors) {
                try {
                    video = await utils.waitForElement(selector, 2000);
                    break;
                } catch (e) {
                    continue;
                }
            }
            
            if (!video) {
                utils.log('未找到视频元素，继续监听...');
                return false;
            }
            
            state.videoElement = video;
            utils.log('找到视频元素', video);
            
            // 监听视频事件
            video.addEventListener('loadedmetadata', () => {
                utils.log('视频元数据加载完成');
                updateStatus('视频已加载');
            });
            
            video.addEventListener('play', () => {
                state.isPlaying = true;
                state.startTime = Date.now();
                utils.log('视频开始播放');
                updateStatus('正在播放');
                startTimers();
            });
            
            video.addEventListener('pause', () => {
                state.isPlaying = false;
                utils.log('视频暂停');
                updateStatus('已暂停');
                stopTimers();
            });
            
            video.addEventListener('ended', () => {
                state.isPlaying = false;
                utils.log('视频播放结束');
                updateStatus('播放完成');
                stopTimers();
            });
            
            // 设置默认速度
            setVideoSpeed(CONFIG.defaultSpeed);
            
            return true;
        } catch (error) {
            utils.log('初始化视频时出错', error);
            return false;
        }
    }
    
    // 设置视频播放速度
    function setVideoSpeed(speed) {
        if (!state.videoElement) return;
        
        try {
            state.videoElement.playbackRate = speed;
            state.currentSpeed = speed;
            
            // 更新界面
            document.getElementById('yxt-current-speed').textContent = speed + 'x';
            document.getElementById('yxt-speed-select').value = speed;
            
            utils.log(`播放速度设置为: ${speed}x`);
            
            // 高速播放时增加同步频率
            if (speed > 3) {
                CONFIG.syncInterval = 500;
            } else {
                CONFIG.syncInterval = 800;
            }
            
            restartTimers();
        } catch (error) {
            utils.log('设置播放速度失败', error);
        }
    }
    
    // 同步播放进度
    function syncProgress() {
        if (!state.videoElement) return;

        try {
            const currentTime = state.videoElement.currentTime;
            const duration = state.videoElement.duration;
            const progress = duration ? (currentTime / duration * 100).toFixed(1) : 0;

            // 更新进度显示
            document.getElementById('yxt-progress').textContent = progress + '%';

            // 触发原生事件
            const events = ['timeupdate', 'progress', 'seeking', 'seeked'];
            events.forEach(eventType => {
                const event = new Event(eventType, { bubbles: true });
                state.videoElement.dispatchEvent(event);
            });

            // 尝试触发云学堂特有的进度事件
            try {
                // 查找并触发可能的进度回调
                if (window.Vue && window.Vue.prototype) {
                    const vueInstances = document.querySelectorAll('[data-v-*]');
                    vueInstances.forEach(el => {
                        if (el.__vue__ && el.__vue__.$emit) {
                            el.__vue__.$emit('timeupdate', { currentTime, duration });
                        }
                    });
                }

                // 触发可能的全局进度事件
                if (window.yxtRPT) {
                    // 云学堂的监控系统
                    window.dispatchEvent(new CustomEvent('video-progress', {
                        detail: { currentTime, duration, progress: progress / 100 }
                    }));
                }
            } catch (e) {
                // 忽略特殊事件触发失败
            }

            state.lastSyncTime = Date.now();
            utils.log(`同步进度: ${progress}%`);

        } catch (error) {
            utils.log('同步进度失败', error);
        }
    }
    
    // 更新状态显示
    function updateStatus(status) {
        const statusElement = document.getElementById('yxt-status');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }
    
    // 智能检测规避
    function setupAntiDetection() {
        // 拦截可能的检测函数
        const originalSetInterval = window.setInterval;
        const originalSetTimeout = window.setTimeout;

        // 监控异常的定时器（可能是检测脚本）
        window.setInterval = function(callback, delay, ...args) {
            // 如果是很短的间隔且不是我们的定时器，可能是检测脚本
            if (delay < 100 && !callback.toString().includes('yxt-speed')) {
                utils.log('检测到可疑定时器，延长间隔', delay);
                delay = Math.max(delay, 200); // 最少200ms
            }
            return originalSetInterval.call(this, callback, delay, ...args);
        };

        // 模拟人工操作
        function simulateHumanBehavior() {
            if (!state.videoElement || !state.isPlaying) return;

            // 随机暂停和恢复（模拟思考）
            if (Math.random() < 0.05 && state.currentSpeed > 3) { // 5%概率
                utils.log('模拟人工暂停');
                state.videoElement.pause();
                setTimeout(() => {
                    if (state.videoElement) {
                        state.videoElement.play();
                        utils.log('模拟人工恢复播放');
                    }
                }, Math.random() * 2000 + 500); // 0.5-2.5秒
            }

            // 随机调整音量（模拟用户操作）
            if (Math.random() < 0.02) { // 2%概率
                const originalVolume = state.videoElement.volume;
                state.videoElement.volume = Math.max(0.1, originalVolume - 0.1);
                setTimeout(() => {
                    if (state.videoElement) {
                        state.videoElement.volume = originalVolume;
                    }
                }, 100);
            }
        }

        // 启动人工行为模拟
        setInterval(simulateHumanBehavior, 10000); // 每10秒检查一次
    }

    // 启动定时器
    function startTimers() {
        stopTimers();

        // 添加随机延迟，避免规律性
        const randomDelay = () => Math.random() * 200 + 50; // 50-250ms随机延迟

        state.timers.sync = setInterval(() => {
            setTimeout(syncProgress, randomDelay());
        }, CONFIG.syncInterval);

        state.timers.heartbeat = setInterval(() => {
            if (state.isPlaying) {
                setTimeout(syncProgress, randomDelay());
            }
        }, CONFIG.heartbeatInterval);

        utils.log('定时器已启动');
    }
    
    // 停止定时器
    function stopTimers() {
        Object.values(state.timers).forEach(timer => {
            if (timer) clearInterval(timer);
        });
        utils.log('定时器已停止');
    }
    
    // 重启定时器
    function restartTimers() {
        if (state.isPlaying) {
            startTimers();
        }
    }
    
    // 主初始化函数
    async function main() {
        utils.log('开始初始化...');

        // 设置反检测机制
        setupAntiDetection();

        // 等待页面基本加载
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }

        // 延迟创建控制面板，避免过早被检测
        setTimeout(() => {
            createControlPanel();
        }, Math.random() * 3000 + 2000); // 2-5秒随机延迟

        // 持续尝试初始化视频
        const initVideoLoop = setInterval(async () => {
            const success = await initVideo();
            if (success) {
                clearInterval(initVideoLoop);
                utils.log('✅ 初始化完成');
                updateStatus('就绪');
            }
        }, 2000);

        // 30秒后停止尝试
        setTimeout(() => {
            clearInterval(initVideoLoop);
            utils.log('⚠️ 初始化超时');
        }, 30000);
    }
    
    // 启动脚本
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', main);
    } else {
        main();
    }
    
})();
