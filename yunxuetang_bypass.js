// 云学堂视频加速器 - 绕过限制版
// 专门解决播放器速度限制问题

(function() {
    'use strict';
    
    console.log('%c🚀 云学堂视频加速器 - 绕过限制版启动', 'color: #ff6b35; font-weight: bold; font-size: 16px;');
    
    // 全局变量
    let currentVideo = null;
    let controlPanel = null;
    let syncTimer = null;
    let currentSpeed = 1;
    let bypassMethod = 'frame'; // 绕过方法：frame, seek, hybrid
    
    // 查找视频元素
    function findVideo() {
        const videos = document.querySelectorAll('video');
        console.log(`🔍 找到 ${videos.length} 个视频元素`);
        
        if (videos.length > 0) {
            currentVideo = videos[0];
            console.log('✅ 使用视频元素:', currentVideo);
            return currentVideo;
        }
        return null;
    }
    
    // 创建控制面板
    function createControlPanel() {
        const existing = document.getElementById('yxt-bypass-panel');
        if (existing) existing.remove();
        
        const panel = document.createElement('div');
        panel.id = 'yxt-bypass-panel';
        panel.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            width: 280px !important;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
            color: white !important;
            padding: 16px !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5) !important;
            z-index: 2147483647 !important;
            font-family: Arial, sans-serif !important;
            font-size: 14px !important;
            border: 2px solid rgba(255,255,255,0.3) !important;
        `;
        
        panel.innerHTML = `
            <div style="font-weight: bold; font-size: 16px; margin-bottom: 12px; text-align: center;">
                🔥 视频加速器 (绕过版)
            </div>
            
            <div style="margin-bottom: 12px;">
                <div style="margin-bottom: 8px; font-weight: 500;">绕过方法:</div>
                <select id="bypass-method" style="width: 100%; padding: 6px; border: none; border-radius: 4px; background: rgba(255,255,255,0.9); color: #333;">
                    <option value="frame">帧跳跃模式</option>
                    <option value="seek">时间跳跃模式</option>
                    <option value="hybrid">混合模式</option>
                </select>
            </div>
            
            <div style="margin-bottom: 12px;">
                <div style="margin-bottom: 8px; font-weight: 500;">播放速度:</div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 6px; margin-bottom: 8px;">
                    <button class="speed-btn" data-speed="2" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">2x</button>
                    <button class="speed-btn" data-speed="3" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">3x</button>
                    <button class="speed-btn" data-speed="5" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">5x</button>
                    <button class="speed-btn" data-speed="8" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">8x</button>
                    <button class="speed-btn" data-speed="10" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">10x</button>
                    <button class="speed-btn" data-speed="16" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">16x</button>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px;">
                    <button id="start-bypass" style="padding: 8px; border: none; border-radius: 4px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">开始加速</button>
                    <button id="stop-bypass" style="padding: 8px; border: none; border-radius: 4px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">停止加速</button>
                </div>
            </div>
            
            <div style="font-size: 12px; line-height: 1.4; background: rgba(0,0,0,0.2); padding: 8px; border-radius: 6px;">
                <div>目标速度: <span id="target-speed" style="font-weight: bold; color: #ffeb3b;">1x</span></div>
                <div>绕过状态: <span id="bypass-status" style="font-weight: bold; color: #4caf50;">待机</span></div>
                <div>播放进度: <span id="progress-display" style="font-weight: bold; color: #2196f3;">0%</span></div>
                <div>跳跃次数: <span id="skip-count" style="font-weight: bold; color: #ff9800;">0</span></div>
            </div>
            
            <div style="margin-top: 10px; font-size: 11px; opacity: 0.8; text-align: center;">
                绕过版 v4.0 | 突破播放器限制
            </div>
        `;
        
        document.body.appendChild(panel);
        controlPanel = panel;
        
        console.log('✅ 绕过版控制面板创建成功');
        return panel;
    }
    
    // 绕过播放器限制的核心逻辑
    let bypassTimer = null;
    let skipCount = 0;
    let lastUpdateTime = 0;
    
    // 帧跳跃模式 - 通过快速跳跃实现加速效果
    function frameSkipMode(targetSpeed) {
        if (bypassTimer) clearInterval(bypassTimer);
        
        const skipInterval = Math.max(50, 1000 / targetSpeed); // 根据速度调整跳跃频率
        const skipAmount = (targetSpeed - 1) * 0.1; // 每次跳跃的时间量
        
        bypassTimer = setInterval(() => {
            if (!currentVideo || currentVideo.paused) return;
            
            const currentTime = currentVideo.currentTime;
            const newTime = Math.min(currentTime + skipAmount, currentVideo.duration - 1);
            
            if (newTime > currentTime) {
                currentVideo.currentTime = newTime;
                skipCount++;
                updateSkipCount(skipCount);
                
                // 触发进度事件
                currentVideo.dispatchEvent(new Event('timeupdate', { bubbles: true }));
                currentVideo.dispatchEvent(new Event('progress', { bubbles: true }));
            }
        }, skipInterval);
        
        console.log(`🎯 帧跳跃模式启动: ${targetSpeed}x, 跳跃间隔: ${skipInterval}ms`);
    }
    
    // 时间跳跃模式 - 大幅度时间跳跃
    function timeSeekMode(targetSpeed) {
        if (bypassTimer) clearInterval(bypassTimer);
        
        const seekInterval = 1000; // 每秒跳跃一次
        const seekAmount = targetSpeed - 1; // 每次跳跃的秒数
        
        bypassTimer = setInterval(() => {
            if (!currentVideo || currentVideo.paused) return;
            
            const currentTime = currentVideo.currentTime;
            const newTime = Math.min(currentTime + seekAmount, currentVideo.duration - 1);
            
            if (newTime > currentTime) {
                currentVideo.currentTime = newTime;
                skipCount++;
                updateSkipCount(skipCount);
                
                // 强制触发事件
                ['timeupdate', 'progress', 'seeking', 'seeked'].forEach(eventType => {
                    currentVideo.dispatchEvent(new Event(eventType, { bubbles: true }));
                });
                
                console.log(`⏭️ 时间跳跃: ${currentTime.toFixed(1)}s -> ${newTime.toFixed(1)}s`);
            }
        }, seekInterval);
        
        console.log(`🎯 时间跳跃模式启动: ${targetSpeed}x, 每秒跳跃: ${seekAmount}s`);
    }
    
    // 混合模式 - 结合播放速度和时间跳跃
    function hybridMode(targetSpeed) {
        // 先设置最大可能的播放速度
        currentVideo.playbackRate = Math.min(targetSpeed, 2);
        
        // 如果目标速度超过2倍，使用时间跳跃补充
        if (targetSpeed > 2) {
            const remainingSpeed = targetSpeed - 2;
            timeSeekMode(remainingSpeed + 1);
        }
        
        console.log(`🎯 混合模式启动: 播放速度${Math.min(targetSpeed, 2)}x + 时间跳跃`);
    }
    
    // 启动绕过加速
    function startBypass(speed) {
        stopBypass(); // 先停止之前的加速
        
        currentSpeed = speed;
        updateTargetSpeed(speed);
        updateBypassStatus('运行中');
        skipCount = 0;
        updateSkipCount(0);
        
        switch (bypassMethod) {
            case 'frame':
                frameSkipMode(speed);
                break;
            case 'seek':
                timeSeekMode(speed);
                break;
            case 'hybrid':
                hybridMode(speed);
                break;
        }
        
        console.log(`🚀 开始绕过加速: ${speed}x, 方法: ${bypassMethod}`);
    }
    
    // 停止绕过加速
    function stopBypass() {
        if (bypassTimer) {
            clearInterval(bypassTimer);
            bypassTimer = null;
        }
        
        if (currentVideo) {
            currentVideo.playbackRate = 1;
        }
        
        updateBypassStatus('已停止');
        console.log('⏹️ 绕过加速已停止');
    }
    
    // 设置面板事件
    function setupPanelEvents() {
        if (!controlPanel) return;
        
        // 绕过方法选择
        const methodSelect = controlPanel.querySelector('#bypass-method');
        methodSelect.addEventListener('change', (e) => {
            bypassMethod = e.target.value;
            console.log(`🔧 切换绕过方法: ${bypassMethod}`);
        });
        
        // 速度按钮
        const speedButtons = controlPanel.querySelectorAll('.speed-btn');
        speedButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const speed = parseFloat(btn.dataset.speed);
                startBypass(speed);
                
                // 更新按钮样式
                speedButtons.forEach(b => b.style.background = 'rgba(255,255,255,0.2)');
                btn.style.background = 'rgba(255,255,255,0.5)';
            });
        });
        
        // 开始/停止按钮
        controlPanel.querySelector('#start-bypass').addEventListener('click', () => {
            if (currentSpeed > 1) {
                startBypass(currentSpeed);
            } else {
                startBypass(5); // 默认5倍速
            }
        });
        
        controlPanel.querySelector('#stop-bypass').addEventListener('click', stopBypass);
        
        console.log('✅ 绕过版面板事件设置完成');
    }
    
    // 更新显示函数
    function updateTargetSpeed(speed) {
        const el = controlPanel?.querySelector('#target-speed');
        if (el) el.textContent = speed + 'x';
    }
    
    function updateBypassStatus(status) {
        const el = controlPanel?.querySelector('#bypass-status');
        if (el) el.textContent = status;
    }
    
    function updateProgressDisplay(progress) {
        const el = controlPanel?.querySelector('#progress-display');
        if (el) el.textContent = progress;
    }
    
    function updateSkipCount(count) {
        const el = controlPanel?.querySelector('#skip-count');
        if (el) el.textContent = count;
    }
    
    // 进度同步
    function syncProgress() {
        if (!currentVideo) return;
        
        const currentTime = currentVideo.currentTime;
        const duration = currentVideo.duration;
        const progress = duration ? (currentTime / duration * 100).toFixed(1) + '%' : '0%';
        
        updateProgressDisplay(progress);
    }
    
    // 启动自动同步
    function startAutoSync() {
        setInterval(() => {
            if (currentVideo && !currentVideo.paused) {
                syncProgress();
            }
        }, 500);
    }
    
    // 初始化
    function init() {
        console.log('🔧 开始初始化绕过版...');
        
        createControlPanel();
        
        if (findVideo()) {
            updateBypassStatus('已找到视频');
        } else {
            updateBypassStatus('未找到视频');
        }
        
        setupPanelEvents();
        startAutoSync();
        
        console.log('✅ 绕过版初始化完成');
        console.log('💡 使用说明:');
        console.log('   1. 选择绕过方法（推荐混合模式）');
        console.log('   2. 点击速度按钮开始加速');
        console.log('   3. 观察跳跃次数和进度变化');
        console.log('   4. 点击"停止加速"恢复正常');
    }
    
    // 提供全局访问
    window.yxtBypassController = {
        start: startBypass,
        stop: stopBypass,
        setMethod: (method) => {
            bypassMethod = method;
            console.log(`🔧 设置绕过方法: ${method}`);
        },
        getVideo: () => currentVideo
    };
    
    // 启动
    init();
    
    console.log('🎉 绕过版加载完成！');
    console.log('🔥 这个版本会通过时间跳跃来实现真正的高速播放');
    
})();
