// 云学堂视频加速器 - 最终解决方案
// 基于深度分析的完整解决方案

(function() {
    'use strict';
    
    console.log('%c🚀 云学堂视频加速器 - 最终解决方案启动', 'color: #ff6b35; font-weight: bold; font-size: 16px;');
    
    // 全局变量
    let currentVideo = null;
    let controlPanel = null;
    let bypassTimer = null;
    let currentSpeed = 1;
    
    // 学习时间追踪
    let sessionStartTime = 0;
    let lastApiCallTime = 0;
    let totalRealWatchTime = 0;
    let skipCount = 0;
    
    // 拦截网络请求
    let originalXHR = window.XMLHttpRequest.prototype.open;
    
    // 查找视频元素
    function findVideo() {
        const videos = document.querySelectorAll('video');
        console.log(`🔍 找到 ${videos.length} 个视频元素`);
        
        if (videos.length > 0) {
            currentVideo = videos[0];
            console.log('✅ 使用视频元素:', currentVideo);
            return currentVideo;
        }
        return null;
    }
    
    // 最终拦截解决方案
    function setupFinalInterception() {
        console.log('🎯 设置最终学习时间拦截...');
        
        // 拦截 XMLHttpRequest
        window.XMLHttpRequest.prototype.open = function(method, url, ...rest) {
            this._url = url;
            this._method = method;
            
            // 拦截学习秒数提交API
            if (url.includes('/kng/study/submit/second')) {
                console.log('🎯 拦截到学习秒数提交API:', url);
                
                const originalSend = this.send;
                this.send = function(data) {
                    if (data) {
                        try {
                            let sendData = JSON.parse(data);
                            console.log('📊 原始学习数据:', sendData);
                            
                            // 计算实际应该提交的时间
                            const now = Date.now();
                            const timeSinceStart = (now - sessionStartTime) / 1000;
                            const timeSinceLastCall = lastApiCallTime > 0 ? (now - lastApiCallTime) / 1000 : 20;
                            
                            // 根据加速倍数计算应该提交的学习时间
                            let shouldSubmitSeconds = Math.floor(timeSinceLastCall);
                            
                            // 如果是加速播放，我们需要提交更多的学习时间
                            if (currentSpeed > 1) {
                                // 按照加速倍数计算学习时间
                                shouldSubmitSeconds = Math.floor(timeSinceLastCall * currentSpeed);
                                // 但不能超过视频实际播放的时间差
                                const maxAllowed = Math.floor(timeSinceLastCall * 2); // 最多2倍，避免太明显
                                shouldSubmitSeconds = Math.min(shouldSubmitSeconds, maxAllowed);
                            }
                            
                            // 确保至少提交原始时间
                            shouldSubmitSeconds = Math.max(shouldSubmitSeconds, sendData.acqSecond || 20);
                            
                            // 修改关键字段
                            if (sendData.acqSecond !== undefined) {
                                const originalAcq = sendData.acqSecond;
                                sendData.acqSecond = shouldSubmitSeconds;
                                console.log(`📝 修改acqSecond: ${originalAcq} -> ${sendData.acqSecond}`);
                            }
                            
                            if (sendData.actualSecond !== undefined) {
                                const originalActual = sendData.actualSecond;
                                sendData.actualSecond = shouldSubmitSeconds;
                                console.log(`📝 修改actualSecond: ${originalActual} -> ${sendData.actualSecond}`);
                            }
                            
                            // 确保速度为正常速度
                            if (sendData.speed !== undefined) {
                                sendData.speed = 1;
                                console.log(`📝 修改speed: -> 1`);
                            }
                            
                            // 更新时间记录
                            lastApiCallTime = now;
                            totalRealWatchTime += shouldSubmitSeconds;
                            
                            data = JSON.stringify(sendData);
                            console.log('✅ 修改后学习数据:', sendData);
                            console.log(`📈 累计学习时间: ${totalRealWatchTime}秒`);
                            
                        } catch (e) {
                            console.log('⚠️ 无法解析学习数据:', e);
                        }
                    }
                    
                    return originalSend.call(this, data);
                };
            }
            
            // 拦截其他可能的学习相关API
            if (url.includes('/studylog/logs') || url.includes('/study/') || url.includes('/learn/')) {
                console.log('🎯 拦截到其他学习API:', url);
                
                const originalSend = this.send;
                this.send = function(data) {
                    if (data) {
                        try {
                            let sendData = JSON.parse(data);
                            console.log('📊 其他学习数据:', sendData);
                            
                            // 尝试修改可能的时间字段
                            const timeFields = ['watchTime', 'studyTime', 'duration', 'playTime', 'learnTime'];
                            timeFields.forEach(field => {
                                if (sendData[field] !== undefined) {
                                    const originalValue = sendData[field];
                                    sendData[field] = totalRealWatchTime;
                                    console.log(`📝 修改${field}: ${originalValue} -> ${sendData[field]}`);
                                }
                            });
                            
                            data = JSON.stringify(sendData);
                            
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                    
                    return originalSend.call(this, data);
                };
            }
            
            return originalXHR.apply(this, [method, url, ...rest]);
        };
        
        console.log('✅ 最终学习时间拦截设置完成');
    }
    
    // 创建控制面板
    function createControlPanel() {
        const existing = document.getElementById('yxt-final-panel');
        if (existing) existing.remove();
        
        const panel = document.createElement('div');
        panel.id = 'yxt-final-panel';
        panel.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            width: 320px !important;
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%) !important;
            color: white !important;
            padding: 16px !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5) !important;
            z-index: 2147483647 !important;
            font-family: Arial, sans-serif !important;
            font-size: 14px !important;
            border: 2px solid rgba(255,255,255,0.3) !important;
        `;
        
        panel.innerHTML = `
            <div style="font-weight: bold; font-size: 16px; margin-bottom: 12px; text-align: center;">
                🔥 最终视频加速器
            </div>
            
            <div style="margin-bottom: 12px;">
                <div style="margin-bottom: 8px; font-weight: 500;">播放速度:</div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 6px; margin-bottom: 8px;">
                    <button class="speed-btn" data-speed="3" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">3x</button>
                    <button class="speed-btn" data-speed="5" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">5x</button>
                    <button class="speed-btn" data-speed="8" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">8x</button>
                    <button class="speed-btn" data-speed="10" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">10x</button>
                    <button class="speed-btn" data-speed="15" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">15x</button>
                    <button class="speed-btn" data-speed="20" style="padding: 8px 4px; border: none; border-radius: 4px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">20x</button>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px;">
                    <button id="start-final" style="padding: 8px; border: none; border-radius: 4px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">开始最终加速</button>
                    <button id="stop-final" style="padding: 8px; border: none; border-radius: 4px; background: rgba(255,255,255,0.3); color: white; cursor: pointer; font-size: 12px; font-weight: bold;">停止加速</button>
                </div>
            </div>
            
            <div style="font-size: 12px; line-height: 1.4; background: rgba(0,0,0,0.2); padding: 8px; border-radius: 6px;">
                <div>目标速度: <span id="target-speed" style="font-weight: bold; color: #ffeb3b;">1x</span></div>
                <div>加速状态: <span id="bypass-status" style="font-weight: bold; color: #4caf50;">待机</span></div>
                <div>播放进度: <span id="progress-display" style="font-weight: bold; color: #2196f3;">0%</span></div>
                <div>会话时间: <span id="session-time" style="font-weight: bold; color: #ff9800;">0秒</span></div>
                <div>学习累计: <span id="total-time" style="font-weight: bold; color: #9c27b0;">0秒</span></div>
                <div>拦截状态: <span id="intercept-status" style="font-weight: bold; color: #00bcd4;">已启用</span></div>
            </div>
            
            <div style="margin-top: 10px; font-size: 11px; opacity: 0.8; text-align: center;">
                最终版 v8.0 | 完整解决方案
            </div>
        `;
        
        document.body.appendChild(panel);
        controlPanel = panel;
        
        console.log('✅ 最终版控制面板创建成功');
        return panel;
    }
    
    // 最终加速模式
    function finalMode(targetSpeed) {
        if (bypassTimer) clearInterval(bypassTimer);
        
        // 设置基础播放速度
        currentVideo.playbackRate = Math.min(targetSpeed, 2);
        
        // 计算跳跃参数
        const skipInterval = 1000; // 每秒检查一次
        const skipAmount = targetSpeed > 2 ? (targetSpeed - 2) : 0;
        
        console.log(`🔥 最终模式参数: 基础速度${Math.min(targetSpeed, 2)}x, 跳跃量${skipAmount}s/秒`);
        
        bypassTimer = setInterval(() => {
            if (!currentVideo || currentVideo.paused) return;
            
            const currentTime = currentVideo.currentTime;
            const duration = currentVideo.duration;
            
            // 执行时间跳跃
            if (skipAmount > 0 && currentTime < duration - 5) {
                const newTime = Math.min(currentTime + skipAmount, duration - 1);
                currentVideo.currentTime = newTime;
                skipCount++;
                
                console.log(`⚡ 最终跳跃: ${currentTime.toFixed(1)}s -> ${newTime.toFixed(1)}s`);
                
                // 触发进度事件
                triggerProgressEvents();
            }
            
            // 更新显示
            updateDisplays();
            
        }, skipInterval);
        
        console.log(`🔥 最终模式启动: ${targetSpeed}x`);
    }
    
    // 触发进度事件
    function triggerProgressEvents() {
        if (!currentVideo) return;
        
        const events = ['timeupdate', 'progress', 'seeking', 'seeked'];
        events.forEach(eventType => {
            const event = new Event(eventType, { bubbles: true });
            currentVideo.dispatchEvent(event);
        });
    }
    
    // 启动最终加速
    function startFinal(speed) {
        stopFinal();
        
        currentSpeed = speed;
        sessionStartTime = Date.now();
        lastApiCallTime = 0;
        totalRealWatchTime = 0;
        skipCount = 0;
        
        updateTargetSpeed(speed);
        updateBypassStatus('最终运行中');
        updateSessionTime('0秒');
        updateTotalTime('0秒');
        
        // 启动最终模式
        finalMode(speed);
        
        console.log(`🚀 开始最终加速: ${speed}x`);
    }
    
    // 停止最终加速
    function stopFinal() {
        if (bypassTimer) {
            clearInterval(bypassTimer);
            bypassTimer = null;
        }
        
        if (currentVideo) {
            currentVideo.playbackRate = 1;
        }
        
        updateBypassStatus('已停止');
        console.log('⏹️ 最终加速已停止');
    }
    
    // 设置面板事件
    function setupPanelEvents() {
        if (!controlPanel) return;
        
        // 速度按钮
        const speedButtons = controlPanel.querySelectorAll('.speed-btn');
        speedButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const speed = parseFloat(btn.dataset.speed);
                startFinal(speed);
                
                speedButtons.forEach(b => b.style.background = 'rgba(255,255,255,0.2)');
                btn.style.background = 'rgba(255,255,255,0.5)';
            });
        });
        
        // 开始/停止按钮
        controlPanel.querySelector('#start-final').addEventListener('click', () => {
            startFinal(currentSpeed > 1 ? currentSpeed : 10);
        });
        
        controlPanel.querySelector('#stop-final').addEventListener('click', stopFinal);
        
        console.log('✅ 最终版面板事件设置完成');
    }
    
    // 更新显示
    function updateDisplays() {
        if (!sessionStartTime) return;
        
        const now = Date.now();
        const sessionElapsed = (now - sessionStartTime) / 1000;
        
        updateSessionTime(Math.floor(sessionElapsed) + '秒');
        updateTotalTime(totalRealWatchTime + '秒');
        
        // 更新进度
        if (currentVideo) {
            const progress = currentVideo.duration ? 
                (currentVideo.currentTime / currentVideo.duration * 100).toFixed(1) + '%' : '0%';
            updateProgressDisplay(progress);
        }
    }
    
    // 更新显示函数
    function updateTargetSpeed(speed) {
        const el = controlPanel?.querySelector('#target-speed');
        if (el) el.textContent = speed + 'x';
    }
    
    function updateBypassStatus(status) {
        const el = controlPanel?.querySelector('#bypass-status');
        if (el) el.textContent = status;
    }
    
    function updateProgressDisplay(progress) {
        const el = controlPanel?.querySelector('#progress-display');
        if (el) el.textContent = progress;
    }
    
    function updateSessionTime(time) {
        const el = controlPanel?.querySelector('#session-time');
        if (el) el.textContent = time;
    }
    
    function updateTotalTime(time) {
        const el = controlPanel?.querySelector('#total-time');
        if (el) el.textContent = time;
    }
    
    function updateInterceptStatus(status) {
        const el = controlPanel?.querySelector('#intercept-status');
        if (el) el.textContent = status;
    }
    
    // 启动自动更新
    function startAutoUpdate() {
        setInterval(() => {
            if (sessionStartTime > 0) {
                updateDisplays();
            }
        }, 1000);
    }
    
    // 初始化
    function init() {
        console.log('🔧 开始初始化最终版...');
        
        // 首先设置最终拦截
        setupFinalInterception();
        
        createControlPanel();
        updateInterceptStatus('已启用');
        
        if (findVideo()) {
            updateBypassStatus('已找到视频');
        } else {
            updateBypassStatus('未找到视频');
        }
        
        setupPanelEvents();
        startAutoUpdate();
        
        console.log('✅ 最终版初始化完成');
        console.log('💡 使用说明:');
        console.log('   1. 最终拦截所有学习相关API');
        console.log('   2. 点击速度按钮开始最终加速');
        console.log('   3. 观察"学习累计"时间增长');
        console.log('   4. 确保学习任务正确完成');
        console.log('⚠️ 如果仍然无效，可能需要分析更深层的验证机制');
    }
    
    // 提供全局访问
    window.yxtFinalController = {
        start: startFinal,
        stop: stopFinal,
        getVideo: () => currentVideo,
        getTotalTime: () => totalRealWatchTime,
        getSessionTime: () => sessionStartTime > 0 ? (Date.now() - sessionStartTime) / 1000 : 0
    };
    
    // 启动
    init();
    
    console.log('🎉 最终版加载完成！');
    console.log('🔥 这是基于深度分析的完整解决方案');
    
})();
