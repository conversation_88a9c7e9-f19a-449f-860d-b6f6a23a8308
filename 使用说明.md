# 云学堂视频播放速度增强器

## 📋 功能特点

- 🚀 **突破速度限制**：支持最高16倍速播放（推荐10倍以下）
- 🔄 **智能进度同步**：自动同步播放进度到服务器，确保学习记录正确
- 🎯 **精准控制**：提供1x到16x多档速度选择
- 📊 **实时监控**：显示当前播放状态和进度
- 🛡️ **安全可靠**：模拟正常播放行为，避免被平台检测

## 🔧 安装方法

### 方法一：使用Tampermonkey（推荐）

1. **安装Tampermonkey扩展**
   - Chrome: [Chrome Web Store](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
   - Firefox: [Firefox Add-ons](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/)
   - Edge: [Microsoft Store](https://microsoftedge.microsoft.com/addons/detail/tampermonkey/iikmkjmpaadaobahmlepeloendndfphd)

2. **安装脚本**
   - 点击Tampermonkey图标 → "管理面板"
   - 点击"新建脚本"
   - 删除默认内容，复制粘贴 `yunxuetang_userscript.js` 的全部内容
   - 按 `Ctrl+S` 保存

3. **启用脚本**
   - 确保脚本状态为"已启用"
   - 访问云学堂网站即可自动生效

### 方法二：浏览器控制台（临时使用）

1. 打开云学堂视频页面
2. 按 `F12` 打开开发者工具
3. 切换到"Console"标签
4. 复制粘贴 `yunxuetang_speed_enhancer.js` 的全部内容
5. 按回车执行

## 🎮 使用方法

### 控制面板

脚本运行后，页面右上角会出现一个紫色的控制面板：

```
🚀 播放控制
├── 播放速度选择器 (1x - 16x)
├── 同步进度按钮
├── 重置速度按钮
└── 状态信息显示
```

### 操作步骤

1. **选择播放速度**
   - 在下拉菜单中选择想要的播放速度
   - 推荐速度：
     - 2x-3x：正常快速学习
     - 5x-8x：快速浏览
     - 10x+：极速刷课（需谨慎）

2. **监控播放状态**
   - 观察状态显示：等待视频 → 视频已加载 → 正在播放
   - 查看进度百分比确保正常同步

3. **手动同步（如需要）**
   - 点击"同步进度"按钮强制同步当前进度
   - 在网络不稳定时建议手动同步

## ⚠️ 重要注意事项

### 使用建议

1. **合理使用速度**
   - 建议不超过10倍速，过高速度可能被检测
   - 重要内容建议使用2-3倍速认真学习

2. **网络环境**
   - 确保网络连接稳定
   - 避免在网络不稳定时使用高倍速

3. **学习效果**
   - 高倍速适合复习或刷课，不适合首次学习
   - 重要知识点建议正常速度学习

### 技术原理

脚本通过以下方式工作：

1. **播放速度控制**：直接修改HTML5视频元素的`playbackRate`属性
2. **进度同步**：定期触发`timeupdate`和`progress`事件
3. **状态监控**：监听视频播放状态变化
4. **智能调节**：根据播放速度调整同步频率

### 故障排除

**问题1：控制面板不显示**
- 刷新页面重试
- 检查脚本是否正确安装和启用
- 确认网站域名匹配

**问题2：速度设置无效**
- 等待视频完全加载后再设置速度
- 尝试手动同步进度
- 检查浏览器控制台是否有错误信息

**问题3：进度不同步**
- 点击"同步进度"按钮
- 降低播放速度
- 检查网络连接

**问题4：被平台检测**
- 降低播放速度（建议5倍以下）
- 增加手动操作（暂停、拖拽进度条等）
- 避免长时间连续高速播放

## 🔒 安全性说明

- 脚本仅在本地浏览器运行，不会上传任何数据
- 不会修改平台服务器数据，只是模拟正常播放行为
- 建议在测试环境先试用，确认无问题后正式使用

## 📞 技术支持

如遇到问题，请检查：
1. 浏览器控制台错误信息
2. 脚本版本是否最新
3. 网站是否有更新导致兼容性问题

---

**免责声明**：本脚本仅供学习和研究使用，请遵守相关平台的使用条款。使用者需自行承担使用风险。
